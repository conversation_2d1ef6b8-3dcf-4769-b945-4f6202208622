/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  margin: 0 !important;
  padding: 0 !important;
  border: 0;
  outline: 0;
}

:root {
  /* Primary Brand Colors - Sophisticated Gold Palette */
  --primary-color: #f2d293;
  --primary-light: #f7e0a8;
  --primary-dark: #e8c478;
  --primary-accent: #f5d89f;

  /* Deep Forest Green - Professional & Natural */
  --forest-primary: #2c3e2d;
  --forest-light: #3a4f3b;
  --forest-dark: #1f2b20;
  --forest-muted: #4a5d4b;

  /* Warm Earth Tones */
  --terracotta: #c17b5a;
  --terracotta-light: #d19075;
  --terracotta-dark: #a86b4a;

  /* Sophisticated Neutrals */
  --charcoal-primary: #3a3a3a;
  --charcoal-light: #4f4f4f;
  --charcoal-dark: #2a2a2a;

  /* Sage & Natural Greens */
  --sage-primary: #8b9a8c;
  --sage-light: #a1afa2;
  --sage-dark: #7a8a7b;
  --sage-muted: #9cab9d;

  /* Professional Blue Accents */
  --blue-muted: #5d7a8b;
  --blue-light: #7a94a3;
  --blue-dark: #4a6373;

  /* Warm Creams & Ivories */
  --cream-primary: #faf7f0;
  --cream-light: #fefcf7;
  --cream-dark: #f5f2eb;
  --ivory: #fefcf7;

  /* Text Colors - Enhanced Hierarchy */
  --text-primary: #2c3e2d;
  --text-secondary: #3a3a3a;
  --text-tertiary: #8b9a8c;
  --text-muted: #a1afa2;
  --text-light: #9cab9d;

  /* Background Colors - Warm & Inviting */
  --bg-primary: #fefcf7;
  --bg-secondary: #faf7f0;
  --bg-accent: #f5f2eb;
  --bg-card: #ffffff;

  /* Semantic Colors - Natural Palette */
  --success: #6b8e5a;
  --warning: #d4a574;
  --error: #c17b5a;
  --info: #5d7a8b;

  /* Enhanced Shadows - Warmer Tones */
  --shadow-sm: 0 2px 4px rgba(44, 62, 45, 0.08);
  --shadow-md: 0 4px 12px rgba(44, 62, 45, 0.12);
  --shadow-lg: 0 8px 24px rgba(44, 62, 45, 0.16);
  --shadow-xl: 0 16px 40px rgba(44, 62, 45, 0.2);
  --shadow-warm: 0 4px 20px rgba(242, 210, 147, 0.15);

  /* Transitions */
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  font-family: "Inter", sans-serif;
  font-size: 16px; /* Ensure minimum 16px for mobile readability */
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  overflow-x: hidden;
}

/* Prevent horizontal scrolling on all elements */
* {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  max-width: 100%;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Playfair Display", serif;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

h1 {
  font-size: 3.5rem;
}
h2 {
  font-size: 2.5rem;
}
h3 {
  font-size: 1.8rem;
}
h4 {
  font-size: 1.4rem;
}

p {
  margin-bottom: 1rem;
  font-weight: 400;
  color: var(--text-secondary);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  font-family: "Inter", sans-serif;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  box-shadow: var(--shadow-warm);
  border: 1px solid var(--primary-dark);
  font-weight: 600;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(
    135deg,
    var(--primary-light),
    var(--primary-accent)
  );
  border-color: var(--primary-color);
}

.btn-secondary {
  background: var(--bg-card);
  color: var(--forest-primary);
  border: 2px solid var(--forest-primary);
  box-shadow: var(--shadow-sm);
  font-weight: 500;
}

.btn-secondary:hover {
  background: var(--forest-primary);
  color: var(--bg-card);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--forest-light);
}

/* Header */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: rgba(58, 58, 58, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: var(--transition-normal);
  border-bottom: 1px solid var(--primary-color);
  box-shadow: var(--shadow-sm);
  margin: 0;
  padding: 0;
}

.navbar {
  padding: 1rem 0;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo-img {
  width: 65px;
  height: 65px;
  object-fit: contain;
}

.company-name {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.company-name h1 {
  font-size: 1.8rem;
  color: var(--primary-color);
  margin: 0;
  font-weight: 700;
}

.company-name span {
  font-size: 0.9rem;
  color: #cccccc;
  font-weight: 500;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
}

.nav-link {
  text-decoration: none;
  color: #cccccc;
  font-weight: 500;
  transition: var(--transition-normal);
  position: relative;
  padding: 0.5rem 0;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.phone-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: var(--shadow-warm);
}

.phone-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(
    135deg,
    var(--primary-light),
    var(--primary-accent)
  );
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #ffffff;
  transition: var(--transition-normal);
}

/* Hero Section - Complete Redesign */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 50%, #f0ede6 100%);
  padding-top: 100px;
}

/* Hero Background Effects */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      ellipse at 70% 30%,
      rgba(242, 210, 147, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      ellipse at 30% 70%,
      rgba(44, 62, 45, 0.08) 0%,
      transparent 50%
    );
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(242, 210, 147, 0.1) 2px,
      transparent 2px
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(44, 62, 45, 0.05) 1px,
      transparent 1px
    );
  background-size: 60px 60px, 40px 40px;
  animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(60px, 60px);
  }
}

/* Enhanced Paint Drops Animation - More Varied */
.paint-drops {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.drop {
  position: absolute;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-light) 70%,
    rgba(242, 210, 147, 0.8) 100%
  );
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  animation: dropFall 12s linear infinite;
  opacity: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.drop-1 {
  left: 8%;
  width: 10px;
  height: 10px;
  animation-delay: 0s;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
}

.drop-2 {
  left: 22%;
  width: 7px;
  height: 7px;
  animation-delay: 3s;
  background: linear-gradient(
    135deg,
    var(--terracotta),
    var(--terracotta-light)
  );
}

.drop-3 {
  left: 45%;
  width: 12px;
  height: 12px;
  animation-delay: 6s;
  background: linear-gradient(
    135deg,
    var(--forest-primary),
    rgba(44, 62, 45, 0.8)
  );
}

.drop-4 {
  left: 68%;
  width: 8px;
  height: 8px;
  animation-delay: 9s;
  background: linear-gradient(135deg, var(--sage-primary), var(--sage-light));
}

.drop-5 {
  left: 85%;
  width: 6px;
  height: 6px;
  animation-delay: 1.5s;
  background: linear-gradient(135deg, var(--blue-muted), var(--blue-light));
}

.drop-6 {
  left: 35%;
  width: 9px;
  height: 9px;
  animation-delay: 4.5s;
  background: linear-gradient(135deg, #e74c3c, #ec7063);
}

@keyframes dropFall {
  0% {
    top: -15px;
    opacity: 0;
    transform: rotate(-45deg) scale(0.3);
  }
  5% {
    opacity: 0.8;
    transform: rotate(-45deg) scale(1);
  }
  85% {
    opacity: 0.6;
    transform: rotate(-45deg) scale(1);
  }
  100% {
    top: 100vh;
    opacity: 0;
    transform: rotate(-45deg) scale(0.8);
  }
}

.hero-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 6rem;
  align-items: center;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

/* Hero Main Content */
.hero-main {
  animation: slideInLeft 1s ease-out;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-md);
  animation: pulse 2s infinite;
}

.hero-badge i {
  font-size: 1rem;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.hero-title {
  font-size: 4.5rem;
  color: #3a3a3a;
  margin-bottom: 2rem;
  line-height: 1.1;
  font-weight: 800;
}

.title-line-1 {
  display: block;
  animation: slideInUp 1s ease-out 0.2s both;
}

.title-line-2 {
  display: block;
  animation: slideInUp 1s ease-out 0.4s both;
}

.highlight {
  color: #f2d293;
  position: relative;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: var(--text-secondary);
  margin-bottom: 0rem;
  line-height: 1.6;
  max-width: 500px;
  animation: slideInUp 1s ease-out 0.6s both;
}

.hero-stats {
  display: flex;
  gap: 3rem;
  margin-bottom: 3rem;
  animation: slideInUp 1s ease-out 0.8s both;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--forest-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--forest-primary),
    var(--primary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hero-actions {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
  animation: slideInUp 1s ease-out 1s both;
}

.hero-cta {
  position: relative;
  overflow: hidden;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.hero-cta:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.hero-cta i {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.hero-cta:hover i {
  transform: translateX(5px);
}

.btn-call {
  background: transparent;
  border: 2px solid var(--forest-primary);
  color: var(--forest-primary);
  padding: 1rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-call:hover {
  background: var(--forest-primary);
  color: var(--bg-card);
  transform: translateY(-2px);
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero Visual Elements */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  animation: slideInRight 1s ease-out 0.5s both;
}

/* Simple Premium Showcase */
.simple-premium-showcase {
  position: relative;
  width: 500px;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  animation: gentleFloat 6s ease-in-out infinite;
}

/* Clean Image Holder */
.image-holder {
  position: relative;
  width: 400px;
  height: 400px;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 20px;
  box-shadow: 0 25px 60px rgba(44, 62, 45, 0.15),
    0 12px 30px rgba(44, 62, 45, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(242, 210, 147, 0.2);
  transition: all 0.3s ease;
}

.image-holder:hover {
  border-color: rgba(242, 210, 147, 0.4);
  box-shadow: 0 30px 70px rgba(44, 62, 45, 0.2),
    0 15px 35px rgba(44, 62, 45, 0.12);
}

.image-placeholder {
  text-align: center;
  color: #888;
  padding: 2rem;
}

.image-placeholder i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: rgba(242, 210, 147, 0.7);
  display: block;
}

.image-placeholder span {
  font-size: 1rem;
  font-weight: 500;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Simple Paint Accents */
.paint-accent {
  position: absolute;
  font-size: 2rem;
  animation: paintFloat 8s ease-in-out infinite;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
  user-select: none;
  z-index: 5;
}

.paint-1 {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.paint-2 {
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.paint-3 {
  bottom: 20%;
  left: 15%;
  animation-delay: 4s;
}

/* Page Header */
.page-header {
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    rgba(242, 210, 147, 0.1) 100%
  );
  padding: 8rem 0 4rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 70% 30%,
    rgba(242, 210, 147, 0.15) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.page-header h1 {
  color: #3a3a3a;
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  position: relative;
  z-index: 2;
}

.page-header p {
  color: var(--text-secondary);
  font-size: 1.3rem;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
  position: relative;
  z-index: 2;
}

/* Simple Clean Animations */
@keyframes gentleFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes paintFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) rotate(5deg) scale(1.1);
    opacity: 1;
  }
}

/* Services Overview - Premium Upgrade */
.services-overview {
  padding: 4rem 0;
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    rgba(242, 210, 147, 0.05) 100%
  );
  position: relative;
  overflow: hidden;
}

.services-overview::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 80% 20%,
    rgba(242, 210, 147, 0.1) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.section-header {
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.section-header h2 {
  color: #3a3a3a;
  margin-bottom: 1.5rem;
  font-weight: 800;
  font-size: 3rem;
  position: relative;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 2px;
}

.section-header p {
  font-size: 1.3rem;
  color: var(--text-secondary);
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* Homepage Service Cards - More Compact */
.services-section .service-card {
  background: var(--bg-card);
  padding: 2rem 1.5rem;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  text-align: center;
  border: 1px solid rgba(242, 210, 147, 0.1);
  position: relative;
  overflow: hidden;
  min-height: 280px;
}

/* Services Overview Cards - Compact Homepage Cards */
.services-overview .service-card {
  background: var(--bg-card);
  padding: 2rem 1.5rem;
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  text-align: center;
  border: 1px solid rgba(242, 210, 147, 0.1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: auto;
  height: fit-content;
}

/* Services Page Cards - Full Size */
.service-card {
  background: var(--bg-card);
  padding: 2.5rem 2rem 2rem;
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  text-align: center;
  border: none;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

/* Services Overview Cards Hover */
.services-overview .service-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: rgba(242, 210, 147, 0.3);
}

/* Homepage Service Cards Hover */
.services-section .service-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: rgba(242, 210, 147, 0.3);
}

/* Services Page Cards Hover */
.service-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.3);
  transition: var(--transition-normal);
}

.service-icon i {
  font-size: 2rem;
  color: var(--forest-primary);
}

.service-card h3 {
  color: #3a3a3a;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  font-weight: 600;
}

/* Services Overview Cards - Compact Text */
.services-overview .service-card .service-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  box-shadow: 0 3px 12px rgba(242, 210, 147, 0.25);
}

.services-overview .service-card .service-icon i {
  font-size: 1.5rem;
}

.services-overview .service-card h3 {
  color: #3a3a3a;
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.3;
}

.services-overview .service-card p {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 0;
  font-size: 0.95rem;
}

/* Homepage Service Cards - Compact Text */
.services-section .service-card .service-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1rem;
  box-shadow: 0 3px 12px rgba(242, 210, 147, 0.25);
}

.services-section .service-card .service-icon i {
  font-size: 1.5rem;
}

.services-section .service-card h3 {
  color: #3a3a3a;
  margin-bottom: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.3;
}

.services-section .service-card p {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 0;
  font-size: 0.95rem;
}

/* Services Page Cards - Full Size Text */
.service-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 0;
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.service-features li {
  color: var(--text-secondary);
  padding: 0.4rem 0;
  position: relative;
  padding-left: 1.5rem;
  font-size: 0.95rem;
}

.service-features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
  font-size: 1rem;
}

/* Service Actions */
.service-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
  padding-top: 1.5rem;
}

.service-actions .btn {
  flex: 1;
  padding: 1rem 1.2rem;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-align: center;
  min-height: 48px;
  border: none;
  text-decoration: none;
}

.service-actions .btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
}

.service-actions .btn-secondary {
  background: var(--forest-primary);
  color: var(--bg-card);
}

.service-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.service-actions .btn i {
  font-size: 0.85rem;
}

/* Premium Services Section */
.services-premium {
  padding: 5rem 0 4rem 0;
  background: var(--cream-primary);
  position: relative;
  overflow: hidden;
}

/* Services Page - Add top padding for fixed header with more spacing */
body:not(.homepage) .services-premium {
  padding: 140px 0 80px 0;
}

/* Premium Page Header - Consistent spacing for all pages with more spacing */
.premium-page-header {
  padding: 140px 0 80px 0 !important;
  margin-top: 0 !important;
}

.services-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.03;
}

.services-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 25% 25%,
      var(--primary-color) 2px,
      transparent 2px
    ),
    radial-gradient(
      circle at 75% 75%,
      var(--forest-primary) 1px,
      transparent 1px
    );
  background-size: 60px 60px, 40px 40px;
  background-position: 0 0, 30px 30px;
}

.services-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.services-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.3);
  /* Ensure consistent sizing across all pages */
  min-width: 140px;
  justify-content: center;
}

.services-badge i {
  font-size: 1rem;
}

.services-header h2 {
  color: #3a3a3a;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.services-header p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
}

.services-showcase {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.service-premium-card {
  background: var(--bg-card);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  box-shadow: 0 8px 30px rgba(44, 62, 45, 0.08);
  border: 1px solid rgba(242, 210, 147, 0.15);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1.25rem;
  min-height: 380px;
  width: 100%;
  box-sizing: border-box;
}

.service-premium-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
}

.service-premium-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(44, 62, 45, 0.12);
  border-color: rgba(242, 210, 147, 0.3);
}

.service-premium-icon {
  width: 90px;
  height: 90px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(242, 210, 147, 0.4);
  flex-shrink: 0;
  transition: var(--transition-normal);
  margin-bottom: 0.5rem;
}

.service-premium-icon i {
  font-size: 2.2rem;
  color: var(--forest-primary);
}

.service-premium-card:hover .service-premium-icon {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.5);
}

.service-premium-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
  padding: 0 0.5rem;
}

.service-premium-content h3 {
  color: #3a3a3a;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.3;
  text-align: center;
}

.service-premium-content p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  text-align: center;
}

.service-premium-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: auto;
  min-height: 60px;
  align-items: center;
}

.btn-service-primary,
.btn-service-call {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.75rem 1.25rem;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition-normal);
  border: none;
  cursor: pointer;
}

.btn-service-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  box-shadow: 0 3px 12px rgba(242, 210, 147, 0.3);
}

.btn-service-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 18px rgba(242, 210, 147, 0.4);
}

.btn-service-call {
  background: var(--forest-primary);
  color: var(--bg-card);
  box-shadow: 0 3px 12px rgba(44, 62, 45, 0.2);
}

.btn-service-call:hover {
  background: var(--forest-light);
  transform: translateY(-2px);
  box-shadow: 0 5px 18px rgba(44, 62, 45, 0.3);
}

/* Premium Services CTA */
.services-premium-cta {
  background: linear-gradient(
    135deg,
    var(--bg-card) 0%,
    rgba(254, 252, 247, 0.95) 100%
  );
  border-radius: 25px;
  padding: 3.5rem 2.5rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(44, 62, 45, 0.1);
  border: 2px solid rgba(242, 210, 147, 0.2);
}

.services-premium-cta::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(242, 210, 147, 0.05) 0%,
    transparent 70%
  );
  animation: rotate 20s linear infinite;
}

.premium-cta-content {
  position: relative;
  z-index: 2;
}

.premium-cta-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 6px 20px rgba(242, 210, 147, 0.4);
}

.premium-cta-icon i {
  font-size: 1.5rem;
  color: var(--forest-primary);
}

.premium-cta-content h3 {
  color: #3a3a3a;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.premium-cta-content p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto 2rem;
  line-height: 1.6;
}

.premium-cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-premium-primary,
.btn-premium-call {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition-normal);
  border: none;
  cursor: pointer;
}

.btn-premium-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.3);
}

.btn-premium-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.4);
}

.btn-premium-call {
  background: var(--forest-primary);
  color: var(--bg-card);
  box-shadow: 0 4px 15px rgba(44, 62, 45, 0.2);
}

.btn-premium-call:hover {
  background: var(--forest-light);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(44, 62, 45, 0.3);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Services CTA Section - Premium Light Design */
.services-cta {
  margin-top: 5rem;
  padding: 4rem 3rem;
  background: linear-gradient(
    135deg,
    var(--bg-card) 0%,
    rgba(254, 252, 247, 0.95) 100%
  );
  border-radius: 25px;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  border: 2px solid rgba(242, 210, 147, 0.2);
  text-align: center;
}

.services-cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 50% 30%,
    rgba(242, 210, 147, 0.1) 0%,
    transparent 60%
  );
  pointer-events: none;
}

.services-cta .cta-content {
  position: relative;
  z-index: 2;
}

.services-cta h3 {
  color: #3a3a3a;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  position: relative;
}

.services-cta h3::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 2px;
}

.services-cta p {
  color: var(--text-secondary);
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.services-cta .btn {
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-md);
}

.services-cta .btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.services-cta .btn i {
  font-size: 1rem;
}

/* Why Choose Us - Premium Upgrade */
.why-choose-us {
  padding: 10rem 0;
  background: linear-gradient(
    135deg,
    var(--cream-primary) 0%,
    rgba(250, 247, 240, 0.9) 30%,
    var(--bg-secondary) 70%,
    rgba(254, 252, 247, 0.95) 100%
  );
  position: relative;
  overflow: hidden;
}

.why-choose-us::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      ellipse at 20% 80%,
      rgba(242, 210, 147, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      ellipse at 80% 20%,
      rgba(44, 62, 45, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 50% 50%,
      rgba(242, 210, 147, 0.05) 0%,
      transparent 70%
    );
  pointer-events: none;
}

.why-choose-us::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="premium-texture" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.8" fill="rgba(242,210,147,0.08)"/><circle cx="5" cy="15" r="0.4" fill="rgba(44,62,45,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23premium-texture)"/></svg>');
  animation: float-texture 25s ease-in-out infinite;
  pointer-events: none;
  opacity: 0.4;
}

@keyframes float-texture {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(1deg);
  }
  66% {
    transform: translateY(-10px) rotate(-0.5deg);
  }
}

.content-split {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: start;
  position: relative;
  z-index: 2;
}

.content-left h2 {
  color: #3a3a3a;
  margin-bottom: 2.5rem;
  font-weight: 800;
  font-size: 3.2rem;
  position: relative;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(58, 58, 58, 0.1);
}

.content-left h2::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(
    ellipse at center,
    rgba(242, 210, 147, 0.1) 0%,
    transparent 70%
  );
  border-radius: 15px;
  z-index: -1;
}

.content-left h2::after {
  content: "";
  position: absolute;
  bottom: -20px;
  left: 0;
  width: 80px;
  height: 6px;
  background: linear-gradient(
    90deg,
    var(--primary-color) 0%,
    var(--primary-light) 50%,
    rgba(242, 210, 147, 0.8) 100%
  );
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(242, 210, 147, 0.4);
  animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%,
  100% {
    box-shadow: 0 2px 8px rgba(242, 210, 147, 0.4);
    transform: scaleX(1);
  }
  50% {
    box-shadow: 0 2px 12px rgba(242, 210, 147, 0.6);
    transform: scaleX(1.05);
  }
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 0;
}

.feature {
  background: var(--bg-card);
  padding: 2rem 1.5rem;
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  text-align: center;
  border: none;
  position: relative;
  overflow: hidden;
}

.feature:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.feature i {
  width: 60px;
  height: 60px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: var(--shadow-sm);
  font-size: 1.5rem;
  color: var(--forest-primary);
}

.feature h4 {
  color: #3a3a3a;
  margin-bottom: 1rem;
  font-weight: 600;
  font-size: 1.2rem;
}

.feature p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.stats-card {
  background: linear-gradient(
    135deg,
    var(--bg-card) 0%,
    rgba(254, 252, 247, 0.95) 100%
  );
  padding: 4rem 3rem;
  border-radius: 25px;
  box-shadow: var(--shadow-xl);
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  text-align: center;
  border: 2px solid rgba(242, 210, 147, 0.3);
  position: relative;
  overflow: hidden;
  margin-top: 0.5rem;
  height: fit-content;
}

.stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(242, 210, 147, 0.08) 0%,
    transparent 70%
  );
  pointer-events: none;
}

.stat {
  position: relative;
  z-index: 2;
}

.stat h3 {
  font-size: 4rem;
  background: linear-gradient(
    135deg,
    var(--forest-primary),
    var(--primary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  font-weight: 800;
  position: relative;
}

.stat h3::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

.stat p {
  color: var(--text-secondary);
  opacity: 0.8;
  margin: 0;
  font-weight: 500;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Gallery Section */
.gallery-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 3rem;
}

/* Premium Gallery Hero */
.gallery-hero {
  padding: 12rem 0 8rem;
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    rgba(242, 210, 147, 0.1) 50%,
    var(--bg-accent) 100%
  );
  position: relative;
  overflow: hidden;
}

.gallery-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 30% 20%,
    rgba(242, 210, 147, 0.15) 0%,
    transparent 60%
  );
  pointer-events: none;
}

.gallery-hero .hero-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

.gallery-hero .hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
}

.gallery-hero .hero-badge i {
  font-size: 1.2rem;
}

.gallery-hero h1 {
  color: #3a3a3a;
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 2rem;
  position: relative;
}

.gallery-hero h1::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 5px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 3px;
}

.gallery-hero p {
  font-size: 1.3rem;
  color: var(--text-secondary);
  opacity: 0.9;
  max-width: 700px;
  margin: 0 auto 3rem;
  line-height: 1.7;
}

.gallery-hero .hero-stats {
  display: flex;
  justify-content: center;
  gap: 4rem;
  margin-top: 4rem;
}

.gallery-hero .stat-item {
  text-align: center;
}

.gallery-hero .stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  display: block;
}

.gallery-hero .stat-label {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.8;
}

/* Floating Paint Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.paint-drop {
  position: absolute;
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--forest-primary);
  font-size: 1.5rem;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

.paint-drop-2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.paint-drop-3 {
  top: 30%;
  right: 25%;
  animation-delay: 4s;
}

.paint-drop-4 {
  bottom: 20%;
  left: 20%;
  animation-delay: 1s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Clean Gallery Section */
.clean-gallery {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.gallery-grid-clean {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.gallery-card {
  background: var(--bg-card);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  border: 1px solid rgba(242, 210, 147, 0.1);
}

.gallery-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: rgba(242, 210, 147, 0.3);
}

.card-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
  position: relative;
}

.image-placeholder-clean {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-light) 100%
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--forest-primary);
  text-align: center;
  position: relative;
}

.image-placeholder-clean i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.placeholder-text {
  font-size: 1rem;
  font-weight: 500;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(5px);
}

.card-content {
  padding: 2rem;
}

.card-content h3 {
  color: #3a3a3a;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.card-content p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

/* Quality Promise Section */
.quality-promise {
  margin-top: 5rem;
  padding: 4rem 0;
  background: var(--bg-secondary);
  border-radius: 20px;
  text-align: center;
}

.promise-content h3 {
  color: #3a3a3a;
  font-size: 2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.promise-content p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto 3rem;
}

.promise-features {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.promise-features .feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--forest-primary);
  font-weight: 500;
}

.promise-features .feature i {
  width: 40px;
  height: 40px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--forest-primary);
  font-size: 1.1rem;
}

.gallery-item {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  cursor: pointer;
  aspect-ratio: 4/3;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(44, 62, 45, 0.85),
    rgba(31, 43, 32, 0.9)
  );
  opacity: 0;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: var(--bg-card);
  text-align: center;
  padding: 2rem;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-overlay h4 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-size: 1.3rem;
}

.gallery-overlay p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.gallery-overlay i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.gallery-filter {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid var(--primary-color);
  background: transparent;
  color: var(--forest-primary);
  border-radius: 25px;
  cursor: pointer;
  transition: var(--transition-normal);
  font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--primary-color);
  color: var(--forest-primary);
}

.gallery-lightbox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(44, 62, 45, 0.95);
  z-index: 2000;
  display: none;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.lightbox-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.lightbox-content img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 10px;
}

.lightbox-close {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: var(--bg-card);
  font-size: 2rem;
  cursor: pointer;
  transition: var(--transition-normal);
}

.lightbox-close:hover {
  color: var(--primary-color);
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(254, 252, 247, 0.9);
  border: none;
  color: var(--forest-primary);
  font-size: 2rem;
  padding: 1rem;
  cursor: pointer;
  transition: var(--transition-normal);
  border-radius: 50%;
  box-shadow: var(--shadow-md);
}

.lightbox-nav:hover {
  background: var(--primary-color);
  color: var(--forest-primary);
}

.lightbox-prev {
  left: -60px;
}

.lightbox-next {
  right: -60px;
}

/* CTA Section */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    rgba(242, 210, 147, 0.15) 100%
  );
  color: var(--forest-primary);
  text-align: center;
  position: relative;
  border-top: 3px solid var(--primary-color);
}

.cta-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(242,210,147,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(242,210,147,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.cta-content {
  position: relative;
  z-index: 1;
}

.cta-content h2 {
  color: #3a3a3a;
  margin-bottom: 1rem;
  font-weight: 700;
}

.cta-content p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  opacity: 0.9;
  margin-bottom: 2rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-section .btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  box-shadow: var(--shadow-warm);
}

.cta-section .btn-secondary {
  border-color: var(--forest-primary);
  color: var(--forest-primary);
  background: transparent;
}

.cta-section .btn-secondary:hover {
  background: var(--forest-primary);
  color: var(--bg-card);
}

/* Footer */
.footer {
  background: var(--charcoal-primary);
  color: #ffffff;
  padding: 4rem 0 2rem;
  border-top: 3px solid var(--primary-color);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.footer-logo img {
  width: 40px;
  height: 40px;
}

.footer-logo h3 {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.footer-section h4 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.footer-section p {
  color: #cccccc;
  opacity: 0.9;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
  color: #cccccc;
}

.footer-section ul li a {
  color: #cccccc;
  text-decoration: none;
  opacity: 0.9;
  transition: var(--transition-normal);
}

.footer-section ul li a:hover {
  opacity: 1;
  color: var(--primary-color);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #cccccc;
}

.contact-item i {
  color: var(--primary-color);
  width: 20px;
}

.footer-bottom {
  border-top: 1px solid rgba(242, 210, 147, 0.3);
  padding-top: 2rem;
  text-align: center;
  opacity: 0.9;
}

.footer-bottom p {
  color: #f2d293 !important;
  margin: 0;
}

/* Services Page Styles */
.service-features {
  list-style: none;
  padding: 0;
  margin-top: 1.5rem;
}

.service-features li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  opacity: 0.8;
  position: relative;
  padding-left: 1.5rem;
}

.service-features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

.service-btn {
  margin-top: 1.5rem;
  width: 100%;
  justify-content: center;
}

/* Process Section - Premium Upgrade */
.process-section {
  padding: 5rem 0;
  background: linear-gradient(
    135deg,
    var(--cream-primary) 0%,
    var(--bg-secondary) 100%
  );
  position: relative;
  overflow: hidden;
}

.process-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 70% 20%,
    rgba(242, 210, 147, 0.15) 0%,
    transparent 60%
  );
  pointer-events: none;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
  margin-top: 3rem;
  position: relative;
  z-index: 2;
}

.process-step {
  text-align: center;
  position: relative;
  background: linear-gradient(
    135deg,
    var(--bg-card) 0%,
    rgba(254, 252, 247, 0.95) 100%
  );
  padding: 3rem 1.5rem;
  border-radius: 20px;
  box-shadow: var(--shadow-lg);
  border: 2px solid rgba(242, 210, 147, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 280px;
}

.process-step:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.step-number {
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 800;
  margin: -4rem auto 2rem;
  box-shadow: var(--shadow-xl);
  border: 4px solid var(--bg-card);
  position: relative;
  flex-shrink: 0;
}

.step-number::after {
  content: "";
  position: absolute;
  inset: -8px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  opacity: 0.2;
  z-index: -1;
}

.process-step h3 {
  color: #3a3a3a;
  margin-bottom: 1.5rem;
  font-weight: 700;
  font-size: 1.3rem;
  flex-shrink: 0;
}

.process-step p {
  color: var(--text-secondary);
  line-height: 1.6;
  font-size: 0.95rem;
  flex-grow: 1;
  display: flex;
  align-items: center;
  text-align: center;
}

/* Pricing Section */
.pricing-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.pricing-card {
  background: var(--bg-card);
  padding: 2.5rem 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-sm);
  text-align: center;
  position: relative;
  transition: var(--transition-normal);
  border: none;
}

.pricing-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.pricing-card.featured {
  border: 3px solid var(--primary-color);
  transform: scale(1.05);
}

.popular-badge {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--primary-color);
  color: var(--forest-primary);
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
}

.pricing-card h3 {
  color: #3a3a3a;
  margin-bottom: 1rem;
  font-weight: 600;
}

.price {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 2rem;
}

.pricing-card ul {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.pricing-card ul li {
  padding: 0.5rem 0;
  color: var(--text-secondary);
  opacity: 0.8;
  position: relative;
  padding-left: 1.5rem;
}

.pricing-card ul li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
  padding: 8rem 0;
  background: var(--bg-secondary);
  position: relative;
  overflow: hidden;
}

.testimonials-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 50% 20%,
    rgba(242, 210, 147, 0.08) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.testimonials-section .section-header {
  text-align: center;
  margin-bottom: 5rem;
  position: relative;
  z-index: 2;
}

.testimonials-section .section-header h2 {
  color: #3a3a3a;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.testimonials-section .section-header p {
  color: var(--text-secondary);
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
  opacity: 0.9;
  line-height: 1.6;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  position: relative;
  z-index: 2;
  margin-top: 4rem;
}

.testimonial-card {
  background: var(--bg-card);
  border-radius: 20px;
  padding: 3rem 2.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(242, 210, 147, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f2d293, #f7e0a8);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-md);
  border-color: rgba(242, 210, 147, 0.3);
}

.testimonial-card:hover::before {
  opacity: 1;
}

.testimonial-card.featured {
  border: 2px solid rgba(242, 210, 147, 0.3);
  transform: scale(1.02);
  box-shadow: var(--shadow-md);
}

.testimonial-card.featured::before {
  opacity: 1;
}

.testimonial-content {
  position: relative;
}

.stars {
  display: flex;
  gap: 0.3rem;
  margin-bottom: 1.5rem;
  justify-content: center;
}

.stars i {
  color: #f2d293;
  font-size: 1.1rem;
}

.testimonial-card blockquote {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-secondary);
  margin: 0 0 2rem 0;
  font-style: italic;
  position: relative;
  opacity: 0.95;
}

.testimonial-card blockquote::before {
  content: "\201C";
  position: absolute;
  top: -10px;
  left: -15px;
  font-size: 3rem;
  color: rgba(242, 210, 147, 0.3);
  font-family: serif;
  line-height: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-info h4 {
  color: #2c3e2d;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.3rem 0;
}

.author-info span {
  color: var(--text-light);
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Responsive Design for Testimonials */
@media (max-width: 1024px) {
  .testimonials-section {
    padding: 6rem 0;
  }

  .testimonials-section .section-header h2 {
    font-size: 2.5rem;
  }

  .testimonials-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
  }

  .testimonial-card {
    padding: 2.5rem 2rem;
  }
}

@media (max-width: 768px) {
  .testimonials-section {
    padding: 5rem 0;
  }

  .testimonials-section .section-header {
    margin-bottom: 3rem;
  }

  .testimonials-section .section-header h2 {
    font-size: 2.2rem;
  }

  .testimonials-section .section-header p {
    font-size: 1.1rem;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .testimonial-card {
    padding: 2rem 1.5rem;
  }

  .testimonial-card.featured {
    transform: scale(1);
  }

  .testimonial-card blockquote {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .testimonials-section {
    padding: 4rem 0;
  }

  .testimonials-section .section-header h2 {
    font-size: 2rem;
  }

  .testimonial-card {
    padding: 1.5rem 1.25rem;
  }

  .testimonial-card blockquote {
    font-size: 0.95rem;
    line-height: 1.6;
  }

  .author-info h4 {
    font-size: 1rem;
  }

  .author-info span {
    font-size: 0.85rem;
  }
}

/* ===== PREMIUM ABOUT US SECTION ===== */
.premium-about-section {
  padding: 8rem 0;
  background: linear-gradient(
    135deg,
    var(--cream-primary) 0%,
    var(--bg-accent) 100%
  );
  position: relative;
  overflow: hidden;
}

.about-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.05;
  pointer-events: none;
}

.about-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 25% 25%,
    var(--primary-color) 2px,
    transparent 2px
  );
  background-size: 60px 60px;
  animation: patternMove 20s linear infinite;
}

.premium-about-content {
  position: relative;
  z-index: 2;
  max-width: 1000px;
  margin: 0 auto;
}

.about-header {
  text-align: center;
  margin-bottom: 5rem;
}

.about-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(242, 210, 147, 0.3);
  border-radius: 50px;
  padding: 1rem 2rem;
  margin-bottom: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  animation: badgeGlow 4s ease-in-out infinite;
  overflow: hidden;
}

.badge-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(
    45deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50px;
  z-index: -1;
  opacity: 0.6;
  animation: glowPulse 3s ease-in-out infinite;
}

.badge-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #2c3e2d;
  font-weight: 600;
  font-size: 1rem;
  position: relative;
  z-index: 2;
}

.badge-content i {
  font-size: 1.1rem;
  color: var(--primary-color);
}

.about-title {
  color: #3a3a3a;
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 2rem;
}

.title-line-1 {
  display: block;
  font-size: 2.5rem;
  font-weight: 600;
  opacity: 0.9;
  animation: slideInUp 1s ease-out 0.2s both;
}

.title-line-2 {
  display: block;
  font-size: 4rem;
  font-weight: 800;
  animation: slideInUp 1s ease-out 0.4s both;
}

.about-intro {
  font-size: 1.3rem;
  color: var(--text-secondary);
  font-weight: 500;
  line-height: 1.7;
  max-width: 700px;
  margin: 0 auto;
  opacity: 0.9;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.about-highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
  margin-bottom: 5rem;
}

.about-highlight-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(242, 210, 147, 0.2);
  border-radius: 20px;
  padding: 2.5rem 2rem;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 1s ease-out calc(0.8s + var(--delay, 0s)) both;
}

.about-highlight-card:nth-child(1) {
  --delay: 0.1s;
}
.about-highlight-card:nth-child(2) {
  --delay: 0.2s;
}
.about-highlight-card:nth-child(3) {
  --delay: 0.3s;
}

.about-highlight-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.about-highlight-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: rgba(242, 210, 147, 0.4);
}

.about-highlight-card:hover::before {
  transform: scaleX(1);
}

.highlight-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.4);
  transition: all 0.3s ease;
}

.highlight-icon i {
  font-size: 1.8rem;
  color: var(--forest-primary);
}

.about-highlight-card:hover .highlight-icon {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(242, 210, 147, 0.6);
}

.highlight-content h3 {
  color: #2c3e2d;
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.highlight-content p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.9;
}

.about-cta {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(254, 252, 247, 0.9) 100%
  );
  backdrop-filter: blur(15px);
  border: 2px solid rgba(242, 210, 147, 0.2);
  border-radius: 25px;
  padding: 3rem 2.5rem;
  text-align: center;
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 1s ease-out 1.2s both;
}

.about-cta::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(242, 210, 147, 0.1),
    transparent
  );
  animation: shimmer 3s ease-in-out infinite;
  pointer-events: none;
}

.cta-content h3 {
  color: #2c3e2d;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.cta-content p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-buttons .btn {
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  min-width: 180px;
  justify-content: center;
}

.cta-buttons .btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.4);
}

.cta-buttons .btn-secondary {
  background: var(--forest-primary);
  color: var(--bg-card);
  box-shadow: 0 4px 15px rgba(44, 62, 45, 0.3);
}

.cta-buttons .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.cta-buttons .btn i {
  font-size: 0.9rem;
}

/* Paint Drops Animation */
.paint-drops {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.drop {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.6;
}

.drop-1 {
  top: 20%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
}

.drop-2 {
  top: 60%;
  right: 15%;
  animation: float 6s ease-in-out infinite 2s;
}

.drop-3 {
  bottom: 30%;
  left: 80%;
  animation: float 6s ease-in-out infinite 4s;
}

/* Responsive Design */
@media (max-width: 768px) {
  .premium-about-section {
    padding: 6rem 0;
  }

  .about-title {
    font-size: 3rem;
  }

  .title-line-1 {
    font-size: 2rem;
  }

  .title-line-2 {
    font-size: 3rem;
  }

  .about-intro {
    font-size: 1.2rem;
  }

  .about-highlights-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 4rem;
  }

  .about-highlight-card {
    padding: 2rem 1.5rem;
  }

  .highlight-content h3 {
    font-size: 1.3rem;
  }

  .about-cta {
    padding: 2.5rem 2rem;
  }

  .cta-content h3 {
    font-size: 1.8rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .about-title {
    font-size: 2.5rem;
  }

  .title-line-1 {
    font-size: 1.8rem;
  }

  .title-line-2 {
    font-size: 2.5rem;
  }

  .about-intro {
    font-size: 1.1rem;
  }

  .about-highlight-card {
    padding: 1.8rem 1.2rem;
  }

  .highlight-icon {
    width: 60px;
    height: 60px;
  }

  .highlight-icon i {
    font-size: 1.5rem;
  }

  .about-cta {
    padding: 2rem 1.5rem;
  }

  .cta-content h3 {
    font-size: 1.6rem;
  }
}

/* ===== PROCESS SECTION ===== */
.process-section {
  padding: 8rem 0;
  background: var(--bg-secondary);
  position: relative;
}

.process-section .section-header {
  text-align: center;
  margin-bottom: 5rem;
}

.process-section .section-header h2 {
  color: #3a3a3a;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.process-section .section-header p {
  color: var(--text-secondary);
  font-size: 1.2rem;
  opacity: 0.9;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  position: relative;
}

.process-step {
  background: var(--bg-card);
  padding: 2rem 1.5rem;
  border-radius: 20px;
  text-align: center;
  position: relative;
  border: 1px solid rgba(242, 210, 147, 0.15);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.process-step:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-md);
  border-color: rgba(242, 210, 147, 0.3);
}

.step-icon {
  margin: 1.5rem 0 1.5rem;
}

.step-icon i {
  font-size: 2.5rem;
  color: #2c3e2d;
}

.process-step h3 {
  color: #3a3a3a;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.process-step p {
  color: var(--text-secondary);
  line-height: 1.6;
  opacity: 0.9;
}

/* ===== COMPANY STORY SECTION ===== */
.company-story-section {
  padding: 8rem 0;
  background: var(--bg-card);
  position: relative;
  overflow: hidden;
}

.company-story-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 30% 50%,
    rgba(242, 210, 147, 0.06) 0%,
    transparent 60%
  );
  pointer-events: none;
}

.story-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 5rem;
  align-items: center;
  position: relative;
  z-index: 2;
}

.story-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  color: #2c3e2d;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.3);
}

.story-text h2 {
  color: #3a3a3a;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.story-intro {
  font-size: 1.3rem;
  color: #2c3e2d;
  font-weight: 500;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.story-details p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.story-values {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2.5rem;
}

.value-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(242, 210, 147, 0.1);
  border-radius: 12px;
  border-left: 4px solid #f2d293;
  transition: all 0.3s ease;
}

.value-item:hover {
  background: rgba(242, 210, 147, 0.15);
  transform: translateX(5px);
}

.value-item i {
  color: #f2d293;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.value-item span {
  color: #2c3e2d;
  font-weight: 600;
  font-size: 1rem;
}

.story-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.story-image-container {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.story-image {
  width: 100%;
  height: 400px;
  border-radius: 20px;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 100%);
  border: 3px solid rgba(242, 210, 147, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.story-image .image-placeholder {
  text-align: center;
  color: rgba(44, 62, 45, 0.6);
}

.story-image .image-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
}

.story-image .image-placeholder span {
  font-size: 1.2rem;
  font-weight: 600;
}

.story-accent {
  position: absolute;
  font-size: 2rem;
  opacity: 0.7;
  animation: floatAccent 6s ease-in-out infinite;
}

.story-1 {
  top: 10%;
  right: -10%;
  animation-delay: 0s;
}

.story-2 {
  bottom: 15%;
  left: -8%;
  animation-delay: 3s;
}

@keyframes floatAccent {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(10deg);
  }
}

/* ===== PROCESS SECTION ===== */
.process-section {
  padding: 10rem 0;
  background: linear-gradient(135deg, #faf7f0 0%, #f8f5f0 50%, #f5f2eb 100%);
  position: relative;
  overflow: hidden;
}

.process-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 30% 70%,
    rgba(242, 210, 147, 0.12) 0%,
    transparent 60%
  );
  pointer-events: none;
}

.process-section::after {
  content: "";
  position: absolute;
  top: 20%;
  right: 10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(
    circle,
    rgba(242, 210, 147, 0.08) 0%,
    transparent 70%
  );
  border-radius: 50%;
  pointer-events: none;
}

.process-section .section-header {
  text-align: center;
  margin-bottom: 5rem;
}

.process-section .section-header h2 {
  color: #3a3a3a;
  font-size: 3.2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.process-section .section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 2px;
}

.process-section .section-header p {
  color: var(--text-secondary);
  font-size: 1.2rem;
  opacity: 0.9;
}

.process-flow {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 2rem;
  margin-top: 4rem;
  position: relative;
  max-width: 1300px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 2rem;
}

.process-flow::before {
  content: "";
  position: absolute;
  top: 60px;
  left: 8%;
  right: 8%;
  height: 2px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light),
    var(--primary-color)
  );
  z-index: 1;
}

.process-step {
  background: transparent;
  padding: 0;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  flex: 1;
  max-width: 200px;
  z-index: 2;
}

.process-step:hover .step-content {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(44, 62, 45, 0.12);
}

.step-number-badge {
  position: absolute;
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  color: #2c3e2d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  border: 3px solid white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  z-index: 3;
}

.process-step:hover .step-icon-circle {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.5);
}

.step-icon-circle {
  width: 110px;
  height: 110px;
  background: var(--bg-card);
  border: 3px solid var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 6px 20px rgba(242, 210, 147, 0.25);
}

.step-icon-circle::after {
  content: "";
  position: absolute;
  inset: -4px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  opacity: 0.15;
  z-index: -1;
  transition: all 0.3s ease;
}

.step-icon-circle i {
  font-size: 2.8rem;
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.process-step:hover .step-icon-circle {
  transform: scale(1.05);
  border-color: var(--primary-light);
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.4);
}

.process-step:hover .step-icon-circle::after {
  opacity: 0.3;
}

.process-step:hover .step-icon-circle i {
  color: var(--forest-primary);
  transform: scale(1.05);
}

.step-content {
  background: var(--bg-card);
  padding: 2rem 1.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(44, 62, 45, 0.08);
  border: 1px solid rgba(242, 210, 147, 0.15);
  transition: all 0.3s ease;
  margin-top: 1rem;
  min-height: 250px;
  height: 250px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.process-step h3 {
  color: #3a3a3a;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.process-step p {
  color: var(--text-secondary);
  line-height: 1.5;
  opacity: 0.9;
  font-size: 0.9rem;
  margin: 0;
  flex-grow: 1;
}

/* Process Section Responsive */
@media (max-width: 1200px) {
  .process-flow {
    gap: 1.5rem;
    padding: 0 1.5rem;
  }

  .process-step {
    max-width: 180px;
  }

  .step-icon-circle {
    width: 100px;
    height: 100px;
  }

  .step-icon-circle i {
    font-size: 2.5rem;
  }

  .step-content {
    padding: 1.8rem 1.3rem;
    min-height: 250px;
    height: 250px;
    height: 170px;
  }

  .process-step h3 {
    font-size: 1.1rem;
  }

  .process-step p {
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .process-section {
    padding: 6rem 0;
  }

  .process-flow {
    flex-direction: column;
    align-items: center;
    gap: 2.5rem;
    margin-top: 3rem;
    padding: 0 1rem;
  }

  .process-flow::before {
    display: none;
  }

  .process-step {
    max-width: 350px;
    width: 100%;
  }

  .step-icon-circle {
    width: 95px;
    height: 95px;
  }

  .step-icon-circle i {
    font-size: 2.4rem;
  }

  .step-content {
    padding: 1.8rem 1.5rem;
    min-height: 250px;
    height: 250px;
    min-height: 160px;
    height: 160px;
  }

  .process-step h3 {
    font-size: 1.2rem;
  }

  .process-step p {
    font-size: 0.9rem;
  }

  .step-number-badge {
    width: 28px;
    height: 28px;
    font-size: 14px;
    top: -14px;
  }
}

@media (max-width: 480px) {
  .process-section {
    padding: 5rem 0;
  }

  .process-section .section-header h2 {
    font-size: 2.2rem;
  }

  .process-flow {
    gap: 2rem;
    margin-top: 2.5rem;
    padding: 0 0.5rem;
  }

  .process-step {
    max-width: 320px;
  }

  .step-icon-circle {
    width: 85px;
    height: 85px;
    border-width: 3px;
  }

  .step-icon-circle i {
    font-size: 2.2rem;
  }

  .step-content {
    padding: 1.6rem 1.3rem;
    min-height: 150px;
    height: 150px;
  }

  .process-step h3 {
    font-size: 1.15rem;
  }

  .process-step p {
    font-size: 0.88rem;
  }

  .step-number-badge {
    width: 26px;
    height: 26px;
    font-size: 13px;
    top: -13px;
  }
}

/* ===== SERVICE AREAS SECTION ===== */
.service-areas-section {
  padding: 8rem 0;
  background: var(--bg-card);
  position: relative;
  overflow: hidden;
}

.service-areas-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 70% 30%,
    rgba(242, 210, 147, 0.08) 0%,
    transparent 60%
  );
  pointer-events: none;
}

.areas-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 5rem;
  align-items: center;
  position: relative;
  z-index: 2;
}

.areas-text h2 {
  color: #3a3a3a;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.areas-intro {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  line-height: 1.6;
  opacity: 0.9;
}

.areas-features {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 3rem;
}

.area-feature {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 1.5rem;
  background: rgba(242, 210, 147, 0.08);
  border-radius: 15px;
  border-left: 4px solid #f2d293;
  transition: all 0.3s ease;
}

.area-feature:hover {
  background: rgba(242, 210, 147, 0.12);
  transform: translateX(5px);
}

.area-feature i {
  color: #f2d293;
  font-size: 1.5rem;
  margin-top: 0.2rem;
  flex-shrink: 0;
}

.feature-text h4 {
  color: #2c3e2d;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.feature-text p {
  color: var(--text-secondary);
  line-height: 1.6;
  opacity: 0.9;
}

.contact-cta {
  padding: 2rem;
  background: linear-gradient(
    135deg,
    rgba(242, 210, 147, 0.1),
    rgba(247, 224, 168, 0.05)
  );
  border-radius: 15px;
  border: 1px solid rgba(242, 210, 147, 0.2);
  text-align: center;
}

.contact-cta p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.contact-cta strong {
  color: #2c3e2d;
}

.areas-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.areas-map-container {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.illawarra-map {
  width: 100%;
  height: 350px;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 100%);
  border: 3px solid rgba(242, 210, 147, 0.3);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.map-region {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 15px;
  /* Create a more realistic map background */
  background:
    /* Ocean on the right */ linear-gradient(
      to left,
      rgba(52, 152, 219, 0.25) 0%,
      rgba(52, 152, 219, 0.15) 15%,
      transparent 25%
    ),
    /* Land mass with topographical feel */
      radial-gradient(
        ellipse at 25% 40%,
        rgba(139, 195, 74, 0.12) 0%,
        rgba(76, 175, 80, 0.08) 40%,
        transparent 70%
      ),
    /* Mountain ranges inland */
      linear-gradient(
        135deg,
        transparent 0%,
        rgba(76, 175, 80, 0.1) 30%,
        rgba(139, 195, 74, 0.15) 50%,
        transparent 80%
      );
  background-size: 100% 100%, 80% 100%, 70% 90%;
  background-position: right center, left center, left top;
  background-repeat: no-repeat;
}

/* Realistic Illawarra coastline */
.coastline {
  position: absolute;
  right: 18%;
  top: 8%;
  bottom: 8%;
  width: 4px;
  background: linear-gradient(
    to bottom,
    rgba(52, 152, 219, 0.8) 0%,
    rgba(52, 152, 219, 0.6) 25%,
    rgba(52, 152, 219, 0.7) 50%,
    rgba(52, 152, 219, 0.8) 75%,
    rgba(52, 152, 219, 0.6) 100%
  );
  border-radius: 2px;
  box-shadow: 2px 0 8px rgba(52, 152, 219, 0.3);
  transform: rotate(2deg);
}

/* Add coastal bays and curves */
.coastline::before {
  content: "";
  position: absolute;
  right: -3px;
  top: 30%;
  width: 10px;
  height: 25%;
  background: rgba(52, 152, 219, 0.4);
  border-radius: 0 12px 12px 0;
  transform: skewY(-8deg);
}

.coastline::after {
  content: "";
  position: absolute;
  right: -2px;
  top: 65%;
  width: 8px;
  height: 20%;
  background: rgba(52, 152, 219, 0.3);
  border-radius: 0 8px 8px 0;
  transform: skewY(5deg);
}

/* Illawarra Escarpment representation */
.coverage-area {
  position: absolute;
  left: 25px;
  top: 25px;
  right: 25%;
  bottom: 25px;
  background: radial-gradient(
    ellipse at 40% 50%,
    rgba(242, 210, 147, 0.18) 0%,
    rgba(242, 210, 147, 0.12) 40%,
    rgba(242, 210, 147, 0.06) 70%,
    transparent 100%
  );
  border-radius: 15px;
  border: 2px dashed rgba(242, 210, 147, 0.5);
}

/* Add the Illawarra Escarpment */
.coverage-area::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 10%;
  bottom: 10%;
  width: 6px;
  background: linear-gradient(
    to bottom,
    rgba(76, 175, 80, 0.4) 0%,
    rgba(139, 195, 74, 0.6) 30%,
    rgba(76, 175, 80, 0.5) 70%,
    rgba(139, 195, 74, 0.3) 100%
  );
  border-radius: 3px;
  box-shadow: inset 2px 0 4px rgba(76, 175, 80, 0.3);
  transform: rotate(-2deg);
}

/* Add Princes Highway representation */
.coverage-area::after {
  content: "";
  position: absolute;
  left: 15%;
  top: 15%;
  right: 15%;
  bottom: 15%;
  border: 1px dashed rgba(139, 195, 74, 0.4);
  border-radius: 8px;
  background: linear-gradient(
    45deg,
    transparent 48%,
    rgba(139, 195, 74, 0.2) 49%,
    rgba(139, 195, 74, 0.2) 51%,
    transparent 52%
  );
  background-size: 20px 20px;
}

.location-marker {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 3;
}

.location-marker i {
  color: #f2d293;
  font-size: 1.5rem;
  margin-bottom: 0.3rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
}

.location-marker span {
  background: rgba(44, 62, 45, 0.9);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  opacity: 0;
  transform: translateY(-5px);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.location-marker:hover i {
  color: #2c3e2d;
  transform: scale(1.2);
}

.location-marker:hover span {
  opacity: 1;
  transform: translateY(0);
}

/* Position markers geographically accurate to Illawarra region */
.wollongong {
  top: 20%;
  left: 52%;
  /* Largest city, positioned near coast */
}

.shellharbour {
  top: 40%;
  left: 58%;
  /* South of Wollongong, closer to coast */
}

.kiama {
  top: 60%;
  left: 62%;
  /* Further south, coastal position */
}

.nowra {
  top: 80%;
  left: 45%;
  /* Inland from coast, southern end of region */
}

/* Geographic labels */
.geo-label {
  position: absolute;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.7rem;
  font-weight: 600;
  color: rgba(44, 62, 45, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  pointer-events: none;
  z-index: 1;
}

.geo-label i {
  font-size: 0.8rem;
  opacity: 0.8;
}

.ocean-label {
  top: 15%;
  right: 8%;
  color: rgba(52, 152, 219, 0.8);
  transform: rotate(90deg);
  transform-origin: center;
}

.escarpment-label {
  top: 50%;
  left: 8%;
  color: rgba(76, 175, 80, 0.8);
  transform: rotate(-90deg);
  transform-origin: center;
}

.service-radius {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: rgba(242, 210, 147, 0.1);
  border-radius: 12px;
  text-align: center;
}

.service-radius i {
  color: #f2d293;
  font-size: 1.2rem;
}

.service-radius span {
  color: #2c3e2d;
  font-weight: 500;
  font-size: 0.95rem;
}

/* Interactive Map Styles */
.interactive-map {
  width: 100%;
  height: 450px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(242, 210, 147, 0.3);
}

/* Custom marker styles */
.custom-marker {
  background: transparent;
  border: none;
}

.marker-pin {
  width: 30px;
  height: 30px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border: 3px solid white;
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  position: relative;
}

.marker-pin i {
  transform: rotate(45deg);
  color: #2c3e2d;
  font-size: 14px;
  font-weight: bold;
}

.marker-pin::after {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  background: #2c3e2d;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  opacity: 0.3;
}

/* Service info control styling */
.service-info-control {
  background: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(242, 210, 147, 0.3);
  min-width: 200px;
}

.service-info-control h4 {
  margin: 0 0 8px 0;
  color: #2c3e2d;
  font-size: 16px;
  font-weight: 600;
}

.service-info-control h4 i {
  color: #f2d293;
  margin-right: 8px;
}

.service-info-control p {
  margin: 0 0 5px 0;
  color: #3a3a3a;
  font-weight: 500;
  font-size: 14px;
}

.service-info-control small {
  color: #666;
  font-size: 12px;
}

/* Leaflet popup customization */
.leaflet-popup-content-wrapper {
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.leaflet-popup-content {
  margin: 15px;
  font-family: "Inter", sans-serif;
}

.leaflet-popup-content strong {
  color: #2c3e2d;
  font-size: 16px;
}

.leaflet-popup-tip {
  background: white;
}

/* Service Areas Responsive */
@media (max-width: 1200px) {
  .areas-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .areas-map-container {
    max-width: 500px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .service-areas-section {
    padding: 6rem 0;
  }

  .illawarra-map {
    height: 280px;
  }

  .location-marker i {
    font-size: 1.2rem;
  }

  .location-marker span {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }

  .coastline {
    width: 6px;
  }

  .coastline::before {
    font-size: 1rem;
  }

  .geo-label {
    font-size: 0.6rem;
  }

  .geo-label i {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .illawarra-map {
    height: 250px;
  }

  .areas-features {
    gap: 1.5rem;
  }

  .area-feature {
    padding: 1.2rem;
  }

  .service-radius {
    padding: 0.8rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .service-radius span {
    font-size: 0.85rem;
  }

  .interactive-map {
    height: 350px;
  }

  .service-info-control {
    padding: 12px;
    min-width: 180px;
  }

  .service-info-control h4 {
    font-size: 14px;
  }

  .service-info-control p {
    font-size: 13px;
  }
}

/* Contact Page Styles */
.contact-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-info-section h2 {
  color: #3a3a3a;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.contact-methods {
  margin-top: 3rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--bg-card);
  border-radius: 15px;
  box-shadow: var(--shadow-sm);
  border: none;
}

.method-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.method-icon i {
  color: var(--forest-primary);
  font-size: 1.2rem;
}

.method-info h3 {
  color: #3a3a3a;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.method-info p {
  margin-bottom: 0.5rem;
}

.method-info a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.method-info a:hover {
  text-decoration: underline;
}

.method-info span {
  color: var(--text-light);
  font-size: 0.9rem;
}

/* Contact Form */
.contact-form {
  background: var(--bg-card);
  padding: 3rem;
  border-radius: 15px;
  box-shadow: var(--shadow-sm);
  border: none;
}

.contact-form h2 {
  color: #3a3a3a;
  margin-bottom: 1rem;
  font-weight: 700;
}

.contact-form > p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  opacity: 0.8;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #3a3a3a;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid rgba(242, 210, 147, 0.3);
  border-radius: 8px;
  font-size: 1rem;
  transition: var(--transition-normal);
  background: var(--bg-card);
  color: var(--text-secondary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(242, 210, 147, 0.2);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.submit-btn {
  width: 100%;
  margin-top: 1rem;
}

/* FAQ Section */
.faq-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.faq-item {
  background: var(--bg-card);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-sm);
  border: none;
}

.faq-item h3 {
  color: #3a3a3a;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.faq-item p {
  color: var(--text-secondary);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

/* About Page Styles */

/* ===== SIMPLE PREMIUM HERO SECTION ===== */
.simple-hero {
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 100%);
  padding: 8rem 0 6rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.simple-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 50% 20%,
    rgba(242, 210, 147, 0.1) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.simple-hero .container {
  position: relative;
  z-index: 2;
}

.simple-hero .hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.simple-hero .hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  color: #2c3e2d;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.3);
  border: 1px solid rgba(242, 210, 147, 0.2);
}

.simple-hero .hero-badge i {
  font-size: 1rem;
}

.simple-hero h1 {
  font-size: 3.5rem;
  font-weight: 800;
  color: #2c3e2d;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.simple-hero p {
  font-size: 1.2rem;
  color: #3a3a3a;
  line-height: 1.7;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.simple-hero .hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.simple-hero .stat {
  text-align: center;
}

.simple-hero .stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 900;
  color: #2c3e2d;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.simple-hero .stat-label {
  font-size: 0.9rem;
  color: #3a3a3a;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design for Simple Hero */
@media (max-width: 768px) {
  .simple-hero {
    padding: 6rem 0 4rem;
  }

  .simple-hero h1 {
    font-size: 2.5rem;
  }

  .simple-hero p {
    font-size: 1.1rem;
  }

  .simple-hero .hero-stats {
    gap: 2rem;
  }

  .simple-hero .stat-number {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .simple-hero {
    padding: 5rem 0 3rem;
  }

  .simple-hero h1 {
    font-size: 2rem;
  }

  .simple-hero .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }
}

/* ===== ABOUT PAGE WOW HERO SECTION ===== */
.about-wow-hero {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    #faf7f0 0%,
    rgba(242, 210, 147, 0.12) 25%,
    #f5f2eb 50%,
    rgba(44, 62, 45, 0.06) 75%,
    #faf7f0 100%
  );
  display: flex;
  align-items: center;
  padding: 3rem 0;
}

.about-wow-hero .hero-canvas {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Enhanced Background Elements */
.about-wow-hero .canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.about-wow-hero .gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      ellipse at 25% 20%,
      rgba(242, 210, 147, 0.18) 0%,
      transparent 50%
    ),
    radial-gradient(
      ellipse at 75% 80%,
      rgba(44, 62, 45, 0.08) 0%,
      transparent 60%
    ),
    radial-gradient(
      ellipse at 50% 50%,
      rgba(242, 210, 147, 0.05) 0%,
      transparent 70%
    );
}

.about-wow-hero .pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(242, 210, 147, 0.08) 2px,
      transparent 2px
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(44, 62, 45, 0.04) 1px,
      transparent 1px
    );
  background-size: 80px 80px, 50px 50px;
  animation: patternMove 25s linear infinite;
}

/* Enhanced Paint Drops */
.about-wow-hero .paint-drops {
  position: absolute;
  width: 100%;
  height: 100%;
}

.about-wow-hero .drop {
  position: absolute;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50% 50% 50% 0;
  opacity: 0.5;
  animation: dropFloat 10s ease-in-out infinite;
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.3);
}

.about-wow-hero .drop-1 {
  top: 15%;
  left: 8%;
  animation-delay: 0s;
}
.about-wow-hero .drop-2 {
  top: 65%;
  left: 88%;
  animation-delay: 2.5s;
}
.about-wow-hero .drop-3 {
  top: 85%;
  left: 12%;
  animation-delay: 5s;
}
.about-wow-hero .drop-4 {
  top: 25%;
  left: 78%;
  animation-delay: 7.5s;
}
.about-wow-hero .drop-5 {
  top: 45%;
  left: 45%;
  animation-delay: 1.5s;
}

/* Hero Layout */
.about-wow-hero .hero-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  z-index: 2;
  min-height: 85vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  gap: 4rem;
}

.about-wow-hero .hero-content {
  padding: 2rem 0;
  max-width: 800px;
}

/* Premium Badge */
.about-wow-hero .premium-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(242, 210, 147, 0.4);
  border-radius: 50px;
  padding: 1rem 2rem;
  margin-bottom: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
  animation: badgeGlow 4s ease-in-out infinite;
  overflow: hidden;
}

.about-wow-hero .badge-icon {
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: #2c3e2d;
  animation: iconPulse 3s ease-in-out infinite;
}

.about-wow-hero .premium-badge span {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e2d;
  letter-spacing: 0.5px;
}

.about-wow-hero .badge-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shine 4s infinite;
}

@keyframes badgeGlow {
  0%,
  100% {
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
    border-color: rgba(242, 210, 147, 0.4);
  }
  50% {
    box-shadow: 0 15px 50px rgba(242, 210, 147, 0.3);
    border-color: rgba(242, 210, 147, 0.6);
  }
}

/* Hero Title */
.about-wow-hero .hero-title {
  color: #2c3e2d;
  font-size: 4.8rem;
  font-weight: 900;
  margin-bottom: 2rem;
  line-height: 1.1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.08);
}

.about-wow-hero .title-line-1 {
  display: block;
  font-size: 3.2rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  animation: slideInUp 1s ease-out 0.3s both;
}

.about-wow-hero .title-line-2 {
  display: block;
  animation: slideInUp 1s ease-out 0.5s both;
}

.about-wow-hero .title-accent {
  background: linear-gradient(135deg, #f2d293 0%, #f7e0a8 50%, #e8c478 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  font-style: italic;
}

.about-wow-hero .title-accent::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #f2d293, #f7e0a8);
  border-radius: 2px;
  opacity: 0.6;
}

/* Hero Description */
.about-wow-hero .hero-description {
  font-size: 1.3rem;
  color: #3a3a3a;
  line-height: 1.8;
  margin-bottom: 3rem;
  max-width: 600px;
  opacity: 0.95;
  animation: slideInUp 1s ease-out 0.7s both;
}

/* Hero Highlights */
.about-wow-hero .hero-highlights {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3.5rem;
  flex-wrap: wrap;
}

.about-wow-hero .highlight-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  padding: 1rem 1.5rem;
  border-radius: 15px;
  border: 1px solid rgba(242, 210, 147, 0.3);
  transition: all 0.3s ease;
  animation: slideInUp 1s ease-out 0.9s both;
}

.about-wow-hero .highlight-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.25);
  border-color: rgba(242, 210, 147, 0.5);
}

.about-wow-hero .highlight-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: #2c3e2d;
  flex-shrink: 0;
}

.about-wow-hero .highlight-item span {
  font-weight: 600;
  color: #2c3e2d;
  font-size: 0.95rem;
}

/* Hero Actions */
.about-wow-hero .hero-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.about-wow-hero .btn-wow {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1.2rem 2.5rem;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  overflow: hidden;
  min-width: 200px;
  justify-content: center;
  animation: slideInUp 1s ease-out 1.1s both;
}

.about-wow-hero .btn-wow i {
  font-size: 1rem;
}

.about-wow-hero .btn-wow.btn-primary {
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  color: #2c3e2d;
  border: 2px solid transparent;
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.3);
}

.about-wow-hero .btn-wow.btn-secondary {
  background: linear-gradient(135deg, #2c3e2d, #3a4f3b);
  color: white;
  border: 2px solid transparent;
  box-shadow: 0 8px 25px rgba(44, 62, 45, 0.3);
}

.about-wow-hero .btn-wow:hover {
  transform: translateY(-3px);
}

.about-wow-hero .btn-wow.btn-primary:hover {
  box-shadow: 0 12px 35px rgba(242, 210, 147, 0.4);
}

.about-wow-hero .btn-wow.btn-secondary:hover {
  box-shadow: 0 12px 35px rgba(44, 62, 45, 0.4);
}

.about-wow-hero .btn-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s ease;
}

.about-wow-hero .btn-wow:hover .btn-shine {
  left: 100%;
}

/* Hero Visual */
.about-wow-hero .hero-visual {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.about-wow-hero .visual-container {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

/* Main Visual Frame */
.about-wow-hero .main-frame {
  position: relative;
  width: 100%;
  height: 450px;
  border-radius: 30px;
  overflow: hidden;
  animation: frameFloat 8s ease-in-out infinite;
}

.about-wow-hero .frame-glow {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(45deg, #f2d293, #f7e0a8, #f2d293, #f7e0a8);
  border-radius: 35px;
  z-index: -1;
  animation: glowRotate 6s linear infinite;
  opacity: 0.6;
}

.about-wow-hero .frame-content {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 100%);
  border-radius: 25px;
  overflow: hidden;
  border: 3px solid rgba(242, 210, 147, 0.3);
}

/* Paint Canvas About */
.about-wow-hero .paint-canvas-about {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-wow-hero .canvas-strokes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.about-wow-hero .stroke {
  position: absolute;
  border-radius: 8px;
  animation: strokePaint 4s ease-in-out infinite;
}

.about-wow-hero .stroke-1 {
  width: 140px;
  height: 10px;
  background: linear-gradient(90deg, #f2d293, #f7e0a8);
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.about-wow-hero .stroke-2 {
  width: 100px;
  height: 8px;
  background: linear-gradient(90deg, #2c3e2d, rgba(44, 62, 45, 0.8));
  top: 35%;
  right: 20%;
  animation-delay: 0.8s;
}

.about-wow-hero .stroke-3 {
  width: 120px;
  height: 12px;
  background: linear-gradient(90deg, #f7e0a8, #f2d293);
  bottom: 35%;
  left: 10%;
  animation-delay: 1.6s;
}

.about-wow-hero .stroke-4 {
  width: 80px;
  height: 6px;
  background: linear-gradient(90deg, #2c3e2d, #f2d293);
  bottom: 20%;
  right: 25%;
  animation-delay: 2.4s;
}

.about-wow-hero .stroke-5 {
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #f2d293, #2c3e2d);
  top: 50%;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 3.2s;
}

@keyframes strokePaint {
  0%,
  100% {
    transform: scaleX(0);
    opacity: 0;
  }
  50% {
    transform: scaleX(1);
    opacity: 1;
  }
}

.about-wow-hero .canvas-center {
  position: relative;
  z-index: 2;
}

.about-wow-hero .center-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #2c3e2d;
  box-shadow: 0 10px 30px rgba(242, 210, 147, 0.4);
  animation: iconPulse 3s ease-in-out infinite;
  border: 4px solid rgba(255, 255, 255, 0.8);
}

/* Floating Stats */
.about-wow-hero .floating-stats {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.about-wow-hero .stat-bubble {
  position: absolute;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(242, 210, 147, 0.4);
  border-radius: 20px;
  padding: 1rem 0.8rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  animation: bubbleFloat 5s ease-in-out infinite;
  min-width: 70px;
}

.about-wow-hero .stat-bubble .stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 900;
  color: #2c3e2d;
  line-height: 1;
  margin-bottom: 0.3rem;
}

.about-wow-hero .stat-bubble .stat-label {
  font-size: 0.7rem;
  color: #3a3a3a;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.about-wow-hero .stat-1 {
  top: 15%;
  left: -15%;
  animation-delay: 0s;
}

.about-wow-hero .stat-2 {
  top: 60%;
  right: -20%;
  animation-delay: 1.5s;
}

.about-wow-hero .stat-3 {
  bottom: 10%;
  left: -10%;
  animation-delay: 3s;
}

@keyframes bubbleFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(2deg);
  }
}

/* Visual Decorations */
.about-wow-hero .visual-decorations {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.about-wow-hero .floating-tool {
  position: absolute;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #2c3e2d;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(242, 210, 147, 0.3);
  animation: toolFloat 6s ease-in-out infinite;
}

.about-wow-hero .tool-1 {
  top: 5%;
  right: 10%;
  animation-delay: 0s;
}

.about-wow-hero .tool-2 {
  bottom: 15%;
  right: 5%;
  animation-delay: 2s;
}

.about-wow-hero .tool-3 {
  top: 40%;
  left: -8%;
  animation-delay: 4s;
}

@keyframes toolFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

/* Scroll Indicator */
.about-wow-hero .scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: #3a3a3a;
  animation: scrollBounce 2s ease-in-out infinite;
}

.about-wow-hero .scroll-indicator span {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.about-wow-hero .scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(242, 210, 147, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.8);
}

@keyframes scrollBounce {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}

/* Responsive Design for About WOW Hero */
@media (max-width: 1024px) {
  .about-wow-hero .hero-layout {
    gap: 3rem;
    padding: 0 1.5rem;
  }

  .about-wow-hero .hero-content {
    padding: 2rem 0;
  }

  .about-wow-hero .hero-title {
    font-size: 4rem;
  }

  .about-wow-hero .title-line-1 {
    font-size: 2.8rem;
  }

  .about-wow-hero .hero-description {
    font-size: 1.2rem;
    max-width: 100%;
  }

  .about-wow-hero .main-frame {
    height: 350px;
    max-width: 400px;
  }

  .about-wow-hero .floating-stats {
    display: none;
  }

  .about-wow-hero .visual-decorations {
    display: none;
  }
}

@media (max-width: 768px) {
  .about-wow-hero {
    min-height: 90vh;
    padding: 2rem 0;
  }

  .about-wow-hero .hero-layout {
    padding: 0 1rem;
    gap: 3rem;
  }

  .about-wow-hero .hero-content {
    padding: 1.5rem 0;
  }

  .about-wow-hero .hero-title {
    font-size: 3.2rem;
    margin-bottom: 1.5rem;
  }

  .about-wow-hero .title-line-1 {
    font-size: 2.2rem;
  }

  .about-wow-hero .hero-description {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
  }

  .about-wow-hero .hero-highlights {
    gap: 1rem;
    margin-bottom: 2.5rem;
  }

  .about-wow-hero .hero-actions {
    flex-direction: column;
    gap: 1.2rem;
  }

  .about-wow-hero .btn-wow {
    width: 100%;
    justify-content: center;
    padding: 1.2rem 2rem;
  }

  .about-wow-hero .main-frame {
    height: 280px;
  }

  /* Disable animations on mobile for performance */
  .about-wow-hero .drop,
  .about-wow-hero .stroke,
  .about-wow-hero .premium-badge,
  .about-wow-hero .center-icon {
    animation: none;
  }

  .about-wow-hero .title-line-1,
  .about-wow-hero .title-line-2,
  .about-wow-hero .hero-description,
  .about-wow-hero .highlight-item,
  .about-wow-hero .btn-wow {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

@media (max-width: 480px) {
  .about-wow-hero .hero-title {
    font-size: 2.4rem;
  }

  .about-wow-hero .title-line-1 {
    font-size: 1.8rem;
  }

  .about-wow-hero .hero-highlights {
    gap: 0.8rem;
  }

  .about-wow-hero .highlight-item {
    padding: 0.8rem 1.2rem;
  }

  .about-wow-hero .highlight-icon {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .about-wow-hero .main-frame {
    height: 220px;
  }

  .about-wow-hero .scroll-indicator {
    bottom: 1rem;
  }
}

/* ===== PREMIUM WOW HERO SECTION ===== */
.wow-hero {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    #faf7f0 0%,
    rgba(242, 210, 147, 0.15) 25%,
    #f5f2eb 50%,
    rgba(44, 62, 45, 0.08) 75%,
    #faf7f0 100%
  );
  display: flex;
  align-items: center;
  padding: 3rem 0;
}

.hero-canvas {
  position: relative;
  width: 100%;
  height: 100%;
}

/* Animated Background Elements */
.canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* Paint Drops Animation */
.paint-drops {
  position: absolute;
  width: 100%;
  height: 100%;
}

.drop {
  position: absolute;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50% 50% 50% 0;
  opacity: 0.6;
  animation: dropFloat 8s ease-in-out infinite;
}

.drop-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}
.drop-2 {
  top: 60%;
  left: 85%;
  animation-delay: 2s;
}
.drop-3 {
  top: 80%;
  left: 15%;
  animation-delay: 4s;
}
.drop-4 {
  top: 30%;
  left: 75%;
  animation-delay: 6s;
}
.drop-5 {
  top: 50%;
  left: 50%;
  animation-delay: 1s;
}

@keyframes dropFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

/* Brush Strokes */
.brush-strokes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.stroke {
  position: absolute;
  background: linear-gradient(
    45deg,
    rgba(44, 62, 45, 0.1),
    rgba(242, 210, 147, 0.2)
  );
  border-radius: 50px;
  animation: strokeMove 12s linear infinite;
}

.stroke-1 {
  width: 200px;
  height: 8px;
  top: 25%;
  left: -100px;
  animation-delay: 0s;
}

.stroke-2 {
  width: 150px;
  height: 6px;
  top: 65%;
  right: -75px;
  animation-delay: 4s;
}

.stroke-3 {
  width: 180px;
  height: 10px;
  top: 45%;
  left: -90px;
  animation-delay: 8s;
}

@keyframes strokeMove {
  0% {
    transform: translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.3;
  }
  90% {
    opacity: 0.3;
  }
  100% {
    transform: translateX(calc(100vw + 200px)) rotate(360deg);
    opacity: 0;
  }
}

/* Floating Particles */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #f2d293;
  border-radius: 50%;
  animation: particleFloat 6s ease-in-out infinite;
}

.particle-1 {
  top: 15%;
  left: 20%;
  animation-delay: 0s;
}
.particle-2 {
  top: 35%;
  left: 80%;
  animation-delay: 1s;
}
.particle-3 {
  top: 55%;
  left: 25%;
  animation-delay: 2s;
}
.particle-4 {
  top: 75%;
  left: 70%;
  animation-delay: 3s;
}
.particle-5 {
  top: 25%;
  left: 60%;
  animation-delay: 4s;
}
.particle-6 {
  top: 65%;
  left: 40%;
  animation-delay: 5s;
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-30px) scale(1.2);
    opacity: 0.8;
  }
}

/* Hero Layout */
.hero-layout {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 5rem;
  align-items: center;
  position: relative;
  z-index: 2;
  min-height: 85vh;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-content {
  padding: 3rem 0;
}

/* Premium Badge */
.premium-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(242, 210, 147, 0.3);
  border-radius: 50px;
  padding: 0.8rem 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: badgeGlow 3s ease-in-out infinite;
}

.badge-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #f2d293, #f7e0a8, #f2d293);
  border-radius: 50px;
  z-index: -1;
  opacity: 0.6;
  animation: glowPulse 2s ease-in-out infinite;
}

.badge-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2c3e2d;
  font-weight: 600;
  font-size: 0.9rem;
}

.badge-content i {
  color: #f2d293;
  font-size: 1rem;
}

.badge-sparkle {
  position: absolute;
  top: -5px;
  right: -5px;
}

.badge-sparkle span {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #f2d293;
  border-radius: 50%;
  animation: sparkle 2s ease-in-out infinite;
}

.badge-sparkle span:nth-child(1) {
  animation-delay: 0s;
}
.badge-sparkle span:nth-child(2) {
  animation-delay: 0.7s;
  left: 8px;
}
.badge-sparkle span:nth-child(3) {
  animation-delay: 1.4s;
  top: 8px;
}

@keyframes badgeGlow {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes glowPulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Dynamic Title */
.hero-title {
  font-size: 4.8rem;
  font-weight: 900;
  line-height: 1.1;
  margin-bottom: 2rem;
  color: #2c3e2d;
}

.title-main {
  display: block;
  margin-bottom: 0.5rem;
}

.word {
  display: inline-block;
  animation: wordSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}

.word-1 {
  animation-delay: 0.2s;
}
.word-2 {
  animation-delay: 0.4s;
  margin-left: 1rem;
}

.title-sub {
  display: block;
  font-size: 3.5rem;
}

.gradient-text {
  background: linear-gradient(135deg, #2c3e2d 0%, #f2d293 50%, #f7e0a8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 4s ease-in-out infinite;
}

@keyframes wordSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Hero Description */
.hero-description {
  font-size: 1.3rem;
  line-height: 1.7;
  color: #3a3a3a;
  margin-bottom: 3rem;
  max-width: 95%;
  animation: fadeInUp 1s ease-out 0.6s forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero Highlights */
.hero-highlights {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  margin-bottom: 3rem;
  list-style: none;
  padding: 0;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 1.2rem;
  padding: 1rem 1.2rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 1px solid rgba(242, 210, 147, 0.3);
  border-left: 4px solid #f2d293;
  transition: all 0.3s ease;
  animation: slideInLeft 0.8s ease-out forwards;
  opacity: 0;
  transform: translateX(-50px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.highlight-item:nth-child(1) {
  animation-delay: 0.8s;
}
.highlight-item:nth-child(2) {
  animation-delay: 1s;
}
.highlight-item:nth-child(3) {
  animation-delay: 1.2s;
}

.highlight-item:hover {
  transform: translateX(10px);
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.3);
}

.highlight-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2c3e2d;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.highlight-item span {
  font-weight: 600;
  color: #2c3e2d;
  font-size: 1rem;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hero Actions */
.hero-actions {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  align-items: center;
}

.btn-wow {
  position: relative;
  display: flex;
  align-items: center;
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  overflow: hidden;
  border: 2px solid transparent;
  animation: buttonSlide 1s ease-out 1.4s forwards;
  opacity: 0;
  transform: translateY(30px);
}

.btn-wow.btn-primary {
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  color: #2c3e2d;
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.4);
  border: 2px solid rgba(242, 210, 147, 0.3);
}

.btn-wow.btn-secondary {
  background: linear-gradient(135deg, #2c3e2d, #3a4f3b);
  color: white;
  box-shadow: 0 8px 25px rgba(44, 62, 45, 0.3);
  border: 2px solid rgba(44, 62, 45, 0.2);
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.4rem;
  z-index: 2;
  position: relative;
  padding: 0.5rem 1rem;
}

.btn-content i {
  font-size: 1.3rem;
}

.btn-content span {
  font-size: 1.1rem;
  font-weight: 700;
}

.btn-content small {
  font-size: 0.85rem;
  opacity: 0.9;
  font-weight: 500;
}

.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.btn-wow:hover .btn-glow {
  transform: translateX(100%);
}

.btn-wow:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.btn-wow.btn-primary:hover {
  box-shadow: 0 12px 35px rgba(242, 210, 147, 0.5);
}

.btn-wow.btn-secondary:hover {
  box-shadow: 0 12px 35px rgba(44, 62, 45, 0.4);
}

@keyframes buttonSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero Visual */
.hero-visual {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.visual-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

/* Main Visual Frame */
.main-frame {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 25px;
  overflow: hidden;
  animation: frameFloat 6s ease-in-out infinite;
}

.frame-glow {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #f2d293, #f7e0a8, #2c3e2d, #f2d293);
  border-radius: 28px;
  z-index: -1;
  animation: glowRotate 4s linear infinite;
}

.frame-content {
  width: 100%;
  height: 100%;
  border-radius: 25px;
  overflow: hidden;
  position: relative;
}

.visual-placeholder {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 30%,
      rgba(242, 210, 147, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 70%,
      rgba(44, 62, 45, 0.2) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 50% 50%,
      rgba(242, 210, 147, 0.1) 0%,
      transparent 70%
    );
}

.placeholder-content {
  text-align: center;
  z-index: 2;
  position: relative;
}

.placeholder-content i {
  font-size: 4rem;
  color: #f2d293;
  margin-bottom: 1rem;
  animation: iconPulse 2s ease-in-out infinite;
}

.placeholder-content h3 {
  font-size: 1.5rem;
  color: #2c3e2d;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.placeholder-content p {
  color: #3a3a3a;
  font-size: 1rem;
  margin: 0;
}

@keyframes frameFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(1deg);
  }
}

@keyframes glowRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Floating Stats */
.floating-stats {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.stat-bubble {
  position: absolute;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(242, 210, 147, 0.4);
  border-radius: 20px;
  padding: 1.2rem 1rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  animation: bubbleFloat 4s ease-in-out infinite;
  min-width: 80px;
}

.stat-1 {
  top: -20px;
  left: -30px;
  animation-delay: 0s;
}

.stat-2 {
  top: 50%;
  right: -40px;
  animation-delay: 1.3s;
}

.stat-3 {
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 2.6s;
}

.stat-number {
  font-size: 1.6rem;
  font-weight: 900;
  color: #2c3e2d;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: #3a3a3a;
  font-weight: 600;
  margin-top: 0.3rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@keyframes bubbleFloat {
  0%,
  100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-15px) scale(1.05);
  }
}

/* Visual Decorations */
.visual-decorations {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.visual-decorations > div {
  position: absolute;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2c3e2d;
  font-size: 1.5rem;
  animation: decoFloat 5s ease-in-out infinite;
  box-shadow: 0 5px 15px rgba(242, 210, 147, 0.3);
}

.deco-brush {
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.deco-palette {
  bottom: 20%;
  left: 5%;
  animation-delay: 1.7s;
}

.deco-roller {
  top: 60%;
  right: 5%;
  animation-delay: 3.4s;
}

@keyframes decoFloat {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: #3a3a3a;
  animation: scrollBounce 2s ease-in-out infinite;
}

.scroll-text {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  opacity: 0.8;
}

.scroll-arrow {
  font-size: 1.2rem;
  color: #f2d293;
}

@keyframes scrollBounce {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}

/* Responsive Design for WOW Hero */
@media (max-width: 1024px) {
  .hero-layout {
    grid-template-columns: 1fr;
    gap: 4rem;
    text-align: center;
    padding: 0 1.5rem;
  }

  .hero-content {
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 4rem;
  }

  .title-sub {
    font-size: 3.2rem;
  }

  .hero-description {
    font-size: 1.2rem;
    max-width: 100%;
  }

  .main-frame {
    height: 300px;
    max-width: 400px;
    margin: 0 auto;
  }

  .floating-stats {
    display: none;
  }

  .visual-decorations {
    display: none;
  }
}

@media (max-width: 768px) {
  .wow-hero {
    min-height: 90vh;
    padding: 2rem 0;
  }

  .hero-layout {
    padding: 0 1rem;
    gap: 3rem;
  }

  .hero-content {
    padding: 1.5rem 0;
  }

  .hero-title {
    font-size: 3.2rem;
    margin-bottom: 1.5rem;
  }

  .title-sub {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
    max-width: 100%;
    margin-bottom: 2.5rem;
  }

  .hero-highlights {
    gap: 1rem;
    margin-bottom: 2.5rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1.2rem;
  }

  .btn-wow {
    width: 100%;
    justify-content: center;
    padding: 1.2rem 2rem;
  }

  .main-frame {
    height: 250px;
  }

  /* Disable animations on mobile for performance */
  .drop,
  .stroke,
  .particle {
    animation: none;
  }

  .premium-badge {
    animation: none;
  }

  .word {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .hero-description {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .highlight-item {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .btn-wow {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2.2rem;
  }

  .title-sub {
    font-size: 1.8rem;
  }

  .hero-highlights {
    gap: 0.8rem;
  }

  .highlight-item {
    padding: 0.6rem;
  }

  .highlight-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .main-frame {
    height: 200px;
  }

  .scroll-indicator {
    bottom: 1rem;
  }
}
.about-story {
  padding: 6rem 0;
  background: var(--bg-card);
}

.about-highlights {
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.highlight {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: var(--text-secondary);
}

.highlight i {
  color: var(--primary-color);
  font-size: 1.2rem;
  width: 20px;
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 15px;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--forest-primary);
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 400px;
}

.image-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.image-placeholder p {
  font-weight: 600;
  margin: 0;
}

/* Values Section */
.values-section {
  padding: 6rem 0;
  background: var(--bg-secondary);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.value-card {
  background: var(--bg-card);
  padding: 2.5rem 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-sm);
  text-align: center;
  transition: var(--transition-normal);
  border: none;
}

.value-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.value-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: var(--shadow-sm);
}

.value-icon i {
  font-size: 1.8rem;
  color: var(--forest-primary);
}

.value-card h3 {
  color: #3a3a3a;
  margin-bottom: 1rem;
  font-weight: 600;
}

.value-card p {
  color: var(--text-secondary);
  opacity: 0.8;
  line-height: 1.6;
}

/* Team Section */
.team-section {
  padding: 8rem 0;
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    var(--bg-accent) 100%
  );
  position: relative;
  overflow: hidden;
}

.team-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(242,210,147,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(242,210,147,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(242,210,147,0.15)"/></svg>');
  opacity: 0.5;
}

.team-hero {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.team-content {
  padding: 2rem 0;
}

.team-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--navy-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-sm);
}

.team-badge i {
  font-size: 1rem;
}

.team-content h2 {
  color: #3a3a3a;
  font-size: 3rem;
  margin-bottom: 0.5rem;
  line-height: 1.1;
}

.team-subtitle {
  color: var(--text-secondary);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 2rem;
}

.team-description {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 3rem;
}

.team-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem 1rem;
  background: var(--bg-primary);
  border-radius: 15px;
  box-shadow: var(--shadow-md);
  border-top: 3px solid var(--primary-color);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--navy-primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

.team-credentials {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.credential {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-primary);
  border-radius: 10px;
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--primary-color);
}

.credential i {
  color: var(--primary-color);
  font-size: 1.2rem;
  width: 20px;
}

.credential span {
  color: var(--text-primary);
  font-weight: 500;
}

.team-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.team-image-container {
  position: relative;
  width: 350px;
  height: 350px;
}

.team-image {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 2;
}

.team-image .image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  border: 4px solid var(--bg-primary);
  position: relative;
  overflow: hidden;
}

.team-image .image-placeholder::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
}

.team-image .image-placeholder i {
  font-size: 5rem;
  color: var(--forest-primary);
  z-index: 1;
  position: relative;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-icon {
  position: absolute;
  width: 60px;
  height: 60px;
  background: var(--bg-card);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-warm);
  border: 2px solid var(--primary-color);
}

.floating-icon i {
  font-size: 1.5rem;
  color: var(--forest-primary);
}

.floating-icon.paint {
  top: 10%;
  right: -10%;
  animation: float 3s ease-in-out infinite;
}

.floating-icon.brush {
  bottom: 20%;
  left: -10%;
  animation: float 3s ease-in-out infinite 1s;
}

.floating-icon.palette {
  top: 60%;
  right: -5%;
  animation: float 3s ease-in-out infinite 2s;
}

/* Premium About Hero Section */
.premium-about-hero {
  min-height: 80vh;
  padding: 6rem 0 4rem;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.premium-about-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 20%,
      rgba(242, 210, 147, 0.12) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(44, 62, 45, 0.06) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.premium-about-hero .container {
  position: relative;
  z-index: 2;
}

.premium-about-hero .hero-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 70vh;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.premium-about-hero .hero-content {
  padding: 2rem 0;
}

.premium-about-hero .hero-badge {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(242, 210, 147, 0.3);
  border-radius: 50px;
  padding: 1rem 2rem;
  margin-bottom: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  animation: badgeGlow 4s ease-in-out infinite;
  overflow: hidden;
}

.premium-about-hero .badge-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #f2d293, #f7e0a8, #f2d293);
  border-radius: 50px;
  z-index: -1;
  opacity: 0.6;
  animation: glowPulse 3s ease-in-out infinite;
}

.premium-about-hero .badge-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #2c3e2d;
  font-weight: 600;
  font-size: 1rem;
  position: relative;
  z-index: 2;
}

.premium-about-hero .badge-content i {
  font-size: 1.1rem;
}

.premium-about-hero .hero-title {
  color: #3a3a3a;
  font-size: 4.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 2rem;
}

.premium-about-hero .title-line-1 {
  display: block;
  font-size: 3rem;
  font-weight: 600;
  opacity: 0.9;
  animation: slideInUp 1s ease-out 0.2s both;
}

.premium-about-hero .title-line-2 {
  display: block;
  font-size: 4.5rem;
  font-weight: 800;
  animation: slideInUp 1s ease-out 0.4s both;
}

.premium-about-hero .title-accent {
  display: block;
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--primary-color);
  animation: slideInUp 1s ease-out 0.6s both;
}

.premium-about-hero .hero-description {
  color: var(--text-secondary);
  font-size: 1.3rem;
  line-height: 1.7;
  margin-bottom: 3rem;
  max-width: 90%;
  animation: fadeInUp 1s ease-out 0.8s both;
}

.premium-about-hero .hero-highlights {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  margin-bottom: 3rem;
}

.premium-about-hero .highlight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  padding: 1rem 1.5rem;
  border-radius: 50px;
  border: 1px solid rgba(242, 210, 147, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  animation: fadeInUp 1s ease-out calc(1s + var(--delay, 0s)) both;
}

.premium-about-hero .highlight-item:nth-child(1) {
  --delay: 0.1s;
}
.premium-about-hero .highlight-item:nth-child(2) {
  --delay: 0.2s;
}
.premium-about-hero .highlight-item:nth-child(3) {
  --delay: 0.3s;
}

.premium-about-hero .highlight-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: rgba(242, 210, 147, 0.4);
}

.premium-about-hero .highlight-icon {
  width: 45px;
  height: 45px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.4);
  transition: all 0.3s ease;
}

.premium-about-hero .highlight-icon i {
  font-size: 1.1rem;
  color: var(--forest-primary);
}

.premium-about-hero .highlight-item:hover .highlight-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(242, 210, 147, 0.6);
}

.premium-about-hero .highlight-item span {
  color: var(--forest-primary);
  font-weight: 600;
  font-size: 0.95rem;
}

.premium-about-hero .hero-buttons {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 1.4s both;
}

/* Premium About Hero Visual */
.premium-about-hero .hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.premium-about-hero .premium-showcase {
  position: relative;
  width: 100%;
  max-width: 500px;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.premium-about-hero .main-frame {
  position: relative;
  width: 400px;
  height: 400px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(250, 247, 240, 0.8) 100%
  );
  border-radius: 30px;
  box-shadow: 0 25px 60px rgba(44, 62, 45, 0.15),
    0 12px 30px rgba(44, 62, 45, 0.1);
  border: 3px solid rgba(242, 210, 147, 0.2);
  transition: all 0.4s ease;
  animation: float 6s ease-in-out infinite;
}

.premium-about-hero .main-frame:hover {
  border-color: rgba(242, 210, 147, 0.4);
  box-shadow: 0 35px 80px rgba(44, 62, 45, 0.2),
    0 18px 40px rgba(44, 62, 45, 0.12);
  transform: translateY(-5px);
}

.premium-about-hero .image-holder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 27px;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  position: relative;
  overflow: hidden;
}

.premium-about-hero .image-holder::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(242, 210, 147, 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
}

.premium-about-hero .image-placeholder {
  text-align: center;
  color: rgba(44, 62, 45, 0.7);
  position: relative;
  z-index: 2;
}

.premium-about-hero .image-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  display: block;
  animation: pulse 2s ease-in-out infinite;
}

.premium-about-hero .image-placeholder span {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--forest-primary);
  display: block;
}

/* Decorative Paint Elements */
.premium-about-hero .decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.premium-about-hero .paint-accent {
  position: absolute;
  width: 60px;
  height: 60px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.4);
  transition: all 0.3s ease;
}

.premium-about-hero .paint-accent i {
  font-size: 1.5rem;
  color: var(--forest-primary);
}

.premium-about-hero .paint-1 {
  top: -15px;
  left: -15px;
  animation: float 4s ease-in-out infinite;
}

.premium-about-hero .paint-2 {
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
  animation: float 4s ease-in-out infinite 1s;
}

.premium-about-hero .paint-3 {
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  animation: float 4s ease-in-out infinite 2s;
}

.premium-about-hero .main-frame:hover .paint-accent {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(242, 210, 147, 0.6);
}

/* Clean About Hero Styles */
.clean-about-hero {
  padding: 6rem 0 4rem !important;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 100%) !important;
  position: relative;
  overflow: hidden;
  min-height: auto !important;
}

.clean-about-hero .hero-layout {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 4rem !important;
  align-items: center !important;
  min-height: 70vh !important;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.clean-about-hero .hero-content {
  padding: 2rem 0 !important;
  text-align: left !important;
}

.clean-about-hero .hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(242, 210, 147, 0.3);
  border-radius: 50px;
  padding: 1rem 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.clean-about-hero .hero-badge i {
  color: #f2d293;
  font-size: 1.1rem;
}

.clean-about-hero .hero-badge span {
  color: #2c3e2d;
  font-weight: 600;
  font-size: 1rem;
}

.clean-about-hero .hero-title {
  color: #3a3a3a !important;
  font-size: 3.5rem !important;
  font-weight: 800 !important;
  margin-bottom: 2rem !important;
  line-height: 1.2 !important;
}

.clean-about-hero .hero-description {
  color: #3a3a3a;
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 500px;
}

.clean-about-hero .hero-highlights {
  display: flex !important;
  flex-direction: column !important;
  gap: 1.2rem !important;
  margin-bottom: 3rem !important;
}

.clean-about-hero .highlight-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  padding: 1rem 1.5rem;
  border-radius: 15px;
  border: 1px solid rgba(242, 210, 147, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.clean-about-hero .highlight-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.25);
  border-color: rgba(242, 210, 147, 0.5);
}

.clean-about-hero .highlight-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2c3e2d;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.clean-about-hero .highlight-item span {
  font-weight: 600;
  color: #2c3e2d;
  font-size: 1rem;
}

.clean-about-hero .hero-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.clean-about-hero .image-container {
  position: relative;
  max-width: 450px;
  width: 100%;
}

.clean-about-hero .image-placeholder {
  width: 100%;
  height: 300px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(250, 247, 240, 0.8) 100%
  );
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  border: 3px solid rgba(242, 210, 147, 0.2);
  box-shadow: 0 15px 40px rgba(44, 62, 45, 0.1);
  transition: all 0.3s ease;
}

.clean-about-hero .image-placeholder:hover {
  border-color: rgba(242, 210, 147, 0.4);
  box-shadow: 0 20px 50px rgba(44, 62, 45, 0.15);
  transform: translateY(-5px);
}

.clean-about-hero .image-placeholder i {
  font-size: 3rem;
  color: #f2d293;
  margin-bottom: 0.5rem;
}

.clean-about-hero .image-placeholder span {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e2d;
}

.clean-about-hero .decorative-brushes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.clean-about-hero .brush-element {
  position: absolute;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #f2d293, #f7e0a8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2c3e2d;
  font-size: 1.2rem;
  box-shadow: 0 8px 20px rgba(242, 210, 147, 0.3);
  animation: float 6s ease-in-out infinite;
}

.clean-about-hero .brush-1 {
  top: -15px;
  right: -15px;
  animation-delay: 0s;
}

.clean-about-hero .brush-2 {
  bottom: -15px;
  left: -15px;
  animation-delay: 3s;
}

.clean-about-hero .hero-buttons {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive Design for Clean About Hero */
@media (max-width: 1024px) {
  .clean-about-hero .hero-layout {
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .clean-about-hero {
    padding: 4rem 0 3rem;
  }

  .clean-about-hero .hero-layout {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .clean-about-hero .hero-content {
    text-align: center;
  }

  .clean-about-hero .hero-title {
    font-size: 2.5rem;
  }

  .clean-about-hero .hero-description {
    font-size: 1.1rem;
    max-width: 100%;
  }

  .clean-about-hero .hero-highlights {
    align-items: center;
  }

  .clean-about-hero .highlight-item {
    max-width: 280px;
  }

  .clean-about-hero .image-container {
    max-width: 350px;
    margin: 0 auto;
  }

  .clean-about-hero .image-placeholder {
    height: 250px;
  }

  .clean-about-hero .hero-buttons {
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
  }

  .clean-about-hero .btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .clean-about-hero .hero-title {
    font-size: 2rem;
  }

  .clean-about-hero .hero-description {
    font-size: 1rem;
  }

  .clean-about-hero .image-placeholder {
    height: 200px;
  }

  .clean-about-hero .image-placeholder i {
    font-size: 2.5rem;
  }

  .clean-about-hero .brush-element {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Clean Header Styles */
.clean-gallery-hero,
.clean-services-header,
.clean-contact-header {
  padding: 6rem 0 4rem;
  background: linear-gradient(135deg, #faf7f0 0%, #f5f2eb 100%);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.clean-gallery-hero .hero-content,
.clean-services-header .header-content,
.clean-contact-header .header-content {
  max-width: 700px;
  margin: 0 auto;
}

.clean-gallery-hero h1,
.clean-services-header h1,
.clean-contact-header h1 {
  color: #3a3a3a;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.clean-gallery-hero p,
.clean-services-header p,
.clean-contact-header p {
  color: #3a3a3a;
  font-size: 1.2rem;
  line-height: 1.6;
  opacity: 0.8;
  margin: 0;
}

/* Responsive Design for Clean Headers */
@media (max-width: 768px) {
  .clean-about-hero,
  .clean-gallery-hero,
  .clean-services-header,
  .clean-contact-header {
    padding: 4rem 0 3rem;
  }

  .clean-about-hero h1,
  .clean-gallery-hero h1,
  .clean-services-header h1,
  .clean-contact-header h1 {
    font-size: 2.5rem;
  }

  .clean-about-hero p,
  .clean-gallery-hero p,
  .clean-services-header p,
  .clean-contact-header p {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .clean-about-hero,
  .clean-gallery-hero,
  .clean-services-header,
  .clean-contact-header {
    padding: 3rem 0 2rem;
  }

  .clean-about-hero h1,
  .clean-gallery-hero h1,
  .clean-services-header h1,
  .clean-contact-header h1 {
    font-size: 2rem;
  }
}

/* Premium Gallery Styles */
.gallery-hero {
  padding: 12rem 0 8rem;
  background: linear-gradient(
    135deg,
    var(--cream-primary) 0%,
    var(--bg-secondary) 100%
  );
  position: relative;
  overflow: hidden;
  text-align: center;
}

.gallery-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 30% 70%,
    rgba(242, 210, 147, 0.2) 0%,
    transparent 60%
  );
  pointer-events: none;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.95rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
}

.hero-badge i {
  font-size: 1.1rem;
}

.gallery-hero h1 {
  color: #3a3a3a;
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  position: relative;
}

.gallery-hero h1::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 2px;
}

.gallery-hero p {
  font-size: 1.3rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto 3rem;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 4rem;
  margin-top: 4rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(
    135deg,
    var(--forest-primary),
    var(--primary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

/* Gallery Filter Section */
.gallery-filter-section {
  padding: 4rem 0;
  background: var(--bg-card);
  border-bottom: 1px solid rgba(242, 210, 147, 0.2);
}

.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: transparent;
  border: 2px solid rgba(242, 210, 147, 0.3);
  border-radius: 50px;
  color: var(--text-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-color: var(--primary-color);
  color: var(--forest-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.filter-btn i {
  font-size: 1rem;
}

/* Premium Gallery Grid */
.premium-gallery {
  padding: 6rem 0;
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    var(--cream-primary) 100%
  );
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.gallery-item {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all 0.4s ease;
  cursor: pointer;
  aspect-ratio: 4/3;
}

.gallery-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.gallery-item.featured {
  grid-column: span 2;
  aspect-ratio: 2/1;
}

.gallery-image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--forest-primary);
  text-align: center;
  padding: 2rem;
  position: relative;
}

.image-placeholder::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 30% 70%,
    rgba(44, 62, 45, 0.1) 0%,
    transparent 50%
  );
}

.image-placeholder i {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.9;
  position: relative;
  z-index: 2;
}

.image-placeholder p {
  font-weight: 700;
  font-size: 1.3rem;
  margin: 0 0 0.5rem 0;
  position: relative;
  z-index: 2;
}

.image-placeholder span {
  font-weight: 500;
  font-size: 1rem;
  opacity: 0.8;
  position: relative;
  z-index: 2;
}

/* Gallery Overlay */
.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(44, 62, 45, 0.9) 0%,
    rgba(44, 62, 45, 0.7) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s ease;
  backdrop-filter: blur(2px);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.overlay-content {
  text-align: center;
  color: var(--bg-card);
  padding: 2rem;
  transform: translateY(20px);
  transition: all 0.4s ease;
}

.gallery-item:hover .overlay-content {
  transform: translateY(0);
}

.overlay-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--bg-card);
}

.overlay-content p {
  font-size: 1rem;
  margin-bottom: 1rem;
  opacity: 0.9;
  line-height: 1.4;
}

.project-tags {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.tag {
  background: var(--primary-color);
  color: var(--forest-primary);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Gallery CTA */
.gallery-cta {
  padding: 6rem 0;
  background: linear-gradient(
    135deg,
    var(--cream-primary) 0%,
    var(--bg-card) 100%
  );
  text-align: center;
  position: relative;
  overflow: hidden;
}

.gallery-cta::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at 50% 50%,
    rgba(242, 210, 147, 0.15) 0%,
    transparent 60%
  );
  pointer-events: none;
}

.gallery-cta .cta-content {
  position: relative;
  z-index: 2;
}

.gallery-cta h2 {
  color: #3a3a3a;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  position: relative;
}

.gallery-cta h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 2px;
}

.gallery-cta p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto 2.5rem;
  line-height: 1.6;
}

.gallery-cta .cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.gallery-cta .btn {
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-md);
}

.gallery-cta .btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.gallery-cta .btn i {
  font-size: 1rem;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Certifications Section - Premium WOW Factor */
.certifications-section {
  padding: 8rem 0;
  background: linear-gradient(
    135deg,
    var(--bg-card) 0%,
    rgba(250, 247, 240, 0.8) 50%,
    var(--bg-card) 100%
  );
  position: relative;
  overflow: hidden;
}

.certifications-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 20%,
      rgba(242, 210, 147, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 80%,
      rgba(44, 62, 45, 0.05) 0%,
      transparent 50%
    );
  pointer-events: none;
}

.certifications-section .container {
  position: relative;
  z-index: 2;
}

.certifications-section .section-header {
  text-align: center;
  margin-bottom: 5rem;
  position: relative;
}

.certifications-section .section-header::after {
  content: "";
  position: absolute;
  bottom: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 2px;
}

.certifications-section h2 {
  color: #3a3a3a;
  font-size: 3.2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.certifications-section .section-header p {
  font-size: 1.3rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.certifications-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 4rem;
}

.cert-card {
  background: var(--bg-card);
  padding: 3.5rem 2.5rem;
  border-radius: 25px;
  box-shadow: 0 10px 40px rgba(44, 62, 45, 0.08);
  border: 2px solid rgba(242, 210, 147, 0.15);
  transition: all 0.4s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
}

.cert-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light),
    var(--primary-color)
  );
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.cert-card:hover::before {
  transform: scaleX(1);
}

.cert-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(44, 62, 45, 0.15);
  border-color: rgba(242, 210, 147, 0.4);
}

.cert-card.featured {
  border-color: var(--primary-color);
  background: linear-gradient(
    135deg,
    var(--bg-card) 0%,
    rgba(242, 210, 147, 0.03) 100%
  );
  transform: scale(1.02);
}

.cert-card.featured::before {
  transform: scaleX(1);
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--primary-light),
    var(--primary-color)
  );
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.cert-icon {
  width: 90px;
  height: 90px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 2rem;
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.4);
  transition: all 0.4s ease;
  position: relative;
}

.cert-icon::after {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(
    45deg,
    var(--primary-color),
    var(--primary-light),
    var(--primary-color)
  );
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.cert-card:hover .cert-icon::after {
  opacity: 0.6;
}

.cert-card:hover .cert-icon {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(242, 210, 147, 0.6);
}

.cert-icon i {
  font-size: 2.5rem;
  color: var(--forest-primary);
  transition: all 0.3s ease;
}

.cert-card:hover .cert-icon i {
  transform: scale(1.1);
}

.cert-card h3 {
  color: #3a3a3a;
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.cert-card p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 2rem;
}

.cert-badge {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(
    135deg,
    var(--forest-primary),
    var(--forest-light)
  );
  color: var(--bg-card);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 15px rgba(44, 62, 45, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cert-badge::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.cert-card:hover .cert-badge::before {
  left: 100%;
}

.cert-card:hover .cert-badge {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(44, 62, 45, 0.4);
}

/* Ultra-Professional Paint Elements Animations - Master Quality */
@keyframes professionalBrushFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.85;
    filter: drop-shadow(0 6px 15px rgba(0, 0, 0, 0.18));
  }
  25% {
    transform: translateY(-18px) rotate(2deg) scale(1.02);
    opacity: 0.95;
    filter: drop-shadow(0 8px 20px rgba(0, 0, 0, 0.22));
  }
  50% {
    transform: translateY(-25px) rotate(-1deg) scale(1.05);
    opacity: 1;
    filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.25));
  }
  75% {
    transform: translateY(-12px) rotate(1.5deg) scale(1.02);
    opacity: 0.9;
    filter: drop-shadow(0 8px 20px rgba(0, 0, 0, 0.2));
  }
}

@keyframes professionalPaletteFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.9;
    filter: drop-shadow(0 8px 20px rgba(0, 0, 0, 0.2));
  }
  30% {
    transform: translateY(-15px) rotate(3deg) scale(1.03);
    opacity: 0.95;
    filter: drop-shadow(0 10px 25px rgba(0, 0, 0, 0.25));
  }
  70% {
    transform: translateY(-8px) rotate(-2deg) scale(1.01);
    opacity: 1;
    filter: drop-shadow(0 12px 30px rgba(0, 0, 0, 0.3));
  }
}

@keyframes professionalColorPulse {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.9;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25),
      inset 0 1px 2px rgba(255, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.15) rotate(5deg);
    opacity: 1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
      inset 0 1px 2px rgba(255, 255, 255, 0.6);
  }
}

/* Enhanced Gentle Float for other elements */
@keyframes gentleFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-12px) rotate(1deg);
    opacity: 0.9;
  }
  66% {
    transform: translateY(-6px) rotate(-0.5deg);
    opacity: 0.8;
  }
}

/* Professional Drip Animations */
@keyframes professionalDrip {
  0%,
  100% {
    opacity: 0.5;
    transform: translateX(-50%) translateY(0) scaleY(1) scaleX(1);
    filter: blur(0px);
  }
  25% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(4px) scaleY(1.1) scaleX(0.95);
    filter: blur(0.5px);
  }
  50% {
    opacity: 0.9;
    transform: translateX(-50%) translateY(10px) scaleY(1.3) scaleX(0.9);
    filter: blur(0px);
  }
  75% {
    opacity: 0.7;
    transform: translateX(-50%) translateY(6px) scaleY(1.15) scaleX(0.95);
    filter: blur(0.3px);
  }
}

@keyframes professionalDripDrop {
  0%,
  85% {
    opacity: 0;
    transform: translateX(-50%) translateY(0) scale(0.3);
  }
  90% {
    opacity: 0.6;
    transform: translateX(-50%) translateY(1px) scale(0.8);
  }
  95% {
    opacity: 0.9;
    transform: translateX(-50%) translateY(3px) scale(1.1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(6px) scale(0.6);
  }
}

@keyframes subtleDrip {
  0%,
  100% {
    opacity: 0.4;
    transform: translateX(-50%) translateY(0) scaleY(1);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-50%) translateY(8px) scaleY(1.2);
  }
}

@keyframes dripDrop {
  0%,
  90% {
    opacity: 0;
    transform: translateX(-50%) translateY(0) scale(0.5);
  }
  95% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(2px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(4px) scale(0.8);
  }
}

@keyframes subtleFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.5;
  }
  25% {
    transform: translateY(-8px) rotate(2deg) scale(1.05);
    opacity: 0.7;
  }
  75% {
    transform: translateY(-3px) rotate(-1deg) scale(0.95);
    opacity: 0.6;
  }
}

@keyframes splatterPulse {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.15) rotate(5deg);
    opacity: 0.8;
  }
}

@keyframes gentleDrop {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  40% {
    transform: translateY(10px) rotate(1deg);
    opacity: 0.8;
  }
  80% {
    transform: translateY(5px) rotate(-0.5deg);
    opacity: 0.7;
  }
}

@keyframes gentlePulse {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.2) rotate(2deg);
    opacity: 0.6;
  }
}

@keyframes subtlePulse {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.08) rotate(1deg);
    opacity: 0.7;
  }
}

/* Enhanced Shape Float Animation */
@keyframes shapeFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.5;
  }
  25% {
    transform: translateY(-15px) rotate(3deg) scale(1.1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-25px) rotate(-2deg) scale(1.05);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-10px) rotate(1deg) scale(0.95);
    opacity: 0.6;
  }
}

@keyframes rollerMove {
  0%,
  100% {
    transform: translateX(0px) rotate(0deg);
  }
  50% {
    transform: translateX(-10px) rotate(-5deg);
  }
}

@keyframes trailFade {
  0%,
  100% {
    opacity: 0;
    width: 20px;
  }
  50% {
    opacity: 1;
    width: 40px;
  }
}

@keyframes bucketTilt {
  0%,
  100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(15deg);
  }
}

@keyframes splash {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Premium Palette Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 4rem;
    text-align: center;
  }

  .hero-title {
    font-size: 3.5rem;
  }

  .hero-subtitle {
    max-width: 100%;
  }

  .hero-stats {
    justify-content: center;
  }

  .simple-premium-showcase {
    width: 350px;
    height: 350px;
  }

  .image-holder {
    width: 260px;
    height: 260px;
  }

  .paint-accent {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .hero {
    padding-top: 80px;
    min-height: 90vh;
  }

  .hero-title {
    font-size: 2.8rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-stats {
    gap: 2rem;
    flex-wrap: wrap;
  }

  .simple-premium-showcase {
    width: 300px;
    height: 300px;
  }

  .image-holder {
    width: 220px;
    height: 220px;
  }

  .paint-accent {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2.2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }

  .simple-premium-showcase {
    width: 280px;
    height: 280px;
  }

  .image-holder {
    width: 200px;
    height: 200px;
  }

  .paint-accent {
    font-size: 1.3rem;
  }
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: var(--forest-primary);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 4px 15px rgba(242, 210, 147, 0.3);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  z-index: 1000;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.scroll-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(242, 210, 147, 0.4);
  background: linear-gradient(
    135deg,
    var(--primary-light),
    var(--primary-accent)
  );
}

/* Mobile responsive for scroll to top */
@media (max-width: 768px) {
  .scroll-to-top {
    width: 45px;
    height: 45px;
    bottom: 20px;
    right: 20px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .scroll-to-top {
    width: 40px;
    height: 40px;
    bottom: 15px;
    right: 15px;
    font-size: 1rem;
  }
}
