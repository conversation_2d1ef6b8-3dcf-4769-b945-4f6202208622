// Mobile Layout Adjustments
document.addEventListener("DOMContentLoaded", function () {
  function adjustMobileLayout() {
    const heroContent = document.querySelector(".hero-content");
    const heroActions = document.querySelector(".hero-actions");
    const heroVisual = document.querySelector(".hero-visual");

    if (!heroContent || !heroActions || !heroVisual) return;

    // Check if we're on mobile (768px or less)
    if (window.innerWidth <= 768) {
      // Move hero-actions after hero-visual if not already moved
      if (heroActions.parentElement.classList.contains("hero-main")) {
        // Clone the buttons with deep clone to preserve all content
        const actionsClone = heroActions.cloneNode(true);
        actionsClone.classList.add("mobile-actions");

        // Ensure all button content is preserved
        const originalButtons = heroActions.querySelectorAll(".btn");
        const clonedButtons = actionsClone.querySelectorAll(".btn");

        originalButtons.forEach((originalBtn, index) => {
          if (clonedButtons[index]) {
            // Copy all attributes and content
            clonedButtons[index].innerHTML = originalBtn.innerHTML;
            clonedButtons[index].className = originalBtn.className;
            clonedButtons[index].href = originalBtn.href;

            // Copy all event listeners by cloning onclick if exists
            if (originalBtn.onclick) {
              clonedButtons[index].onclick = originalBtn.onclick;
            }
          }
        });

        // Hide original buttons
        heroActions.style.display = "none";

        // Insert after hero-visual
        heroVisual.parentNode.insertBefore(
          actionsClone,
          heroVisual.nextSibling
        );
      }
    } else {
      // Desktop - restore original layout
      const mobileActions = document.querySelector(".mobile-actions");
      if (mobileActions) {
        mobileActions.remove();
        heroActions.style.display = "";
      }
    }
  }

  // Run on load
  adjustMobileLayout();

  // Run on resize with debounce
  let resizeTimer;
  window.addEventListener("resize", function () {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(adjustMobileLayout, 250);
  });
});
